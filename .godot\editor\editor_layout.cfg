[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://scripts/Entities/Animal.gd")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://", "res://scripts/", "res://scripts/Entities/", "res://scenes/", "res://scenes/Test/")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=10
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=271
dock_hsplit_3=-270
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray()
current_scene=""
center_split_offset=-289
selected_default_debugger_tab_idx=0
selected_main_editor_idx=2
selected_bottom_panel_item=1

[EditorWindow]

screen=1
mode="maximized"
position=Vector2i(62, 23)
size=Vector2i(1152, 841)

[ScriptEditor]

open_scripts=["res://scripts/Test/AllSystemsTestController.gd", "res://scripts/Entities/Animal.gd", "res://scripts/Systems/Environment/AnimalSystem.gd", "res://scripts/Test/ColonistSystemTestController.gd", "res://scripts/Managers/DataManager.gd", "res://scripts/UI/DataVisualizationUI.gd", "res://scripts/Systems/Social/FactionSystem.gd", "res://scripts/Core/GameManager.gd", "res://scripts/Test/GameSystemsTestController.gd", "res://scripts/Test/GridSystemTestController.gd", "res://scripts/Controllers/MainSceneController.gd", "res://scripts/Systems/Colonist/MedicalSystem.gd", "res://scripts/UI/ResearchUI.gd", "res://scripts/Entities/SimpleColonist.gd", "res://scripts/UI/SimpleUI.gd", "res://scripts/Test/TestMenuController.gd", "res://scripts/Systems/Social/TradeSystem.gd", "res://scripts/Utils/Utils.gd"]
selected_script="res://scripts/Entities/Animal.gd"
open_help=[]
script_split_offset=236
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(345, 139, 1292, 767)
floating_window_screen=1

[ShaderEditor]

open_shaders=[]
split_offset=200
selected_shader=""
text_shader_zoom_factor=1.0
