extends RefCounted
class_name Utils

# 通用工具函数 - 简化常用操作

# 数组工具
static func safe_get(array: Array, index: int, default = null):
	"""安全获取数组元素"""
	return array[index] if index >= 0 and index < array.size() else default

static func random_choice(array: Array):
	"""随机选择数组元素"""
	return array[randi() % array.size()] if array.size() > 0 else null

static func random_range_from_array(range_array: Array):
	"""从包含两个元素的数组中生成随机范围值"""
	if range_array.size() != 2:
		push_error("random_range_from_array requires an array with exactly 2 elements")
		return 0

	var min_val = range_array[0]
	var max_val = range_array[1]

	# 根据数据类型选择合适的随机函数
	if typeof(min_val) == TYPE_INT and typeof(max_val) == TYPE_INT:
		return randi_range(min_val, max_val)
	else:
		return randf_range(float(min_val), float(max_val))

static func shuffle_array(array: Array) -> Array:
	"""打乱数组"""
	var shuffled = array.duplicate()
	for i in range(shuffled.size() - 1, 0, -1):
		var j = randi() % (i + 1)
		var temp = shuffled[i]
		shuffled[i] = shuffled[j]
		shuffled[j] = temp
	return shuffled

static func chunk_array(array: Array, chunk_size: int) -> Array:
	"""将数组分块"""
	var chunks = []
	for i in range(0, array.size(), chunk_size):
		chunks.append(array.slice(i, min(i + chunk_size, array.size())))
	return chunks

static func filter_by_conditions(items: Array, conditions: Dictionary) -> Array:
	"""根据条件过滤数组"""
	return items.filter(func(item): return meets_conditions(item, conditions))

static func meets_conditions(item, conditions: Dictionary) -> bool:
	"""检查对象是否满足条件"""
	for condition in conditions:
		var value = get_nested_property(item, condition)
		var expected = conditions[condition]

		if typeof(expected) == TYPE_DICTIONARY:
			# 支持范围条件 {"min": 10, "max": 20}
			if "min" in expected and value < expected["min"]:
				return false
			if "max" in expected and value > expected["max"]:
				return false
		elif value != expected:
			return false

	return true

static func get_nested_property(obj, property_path: String):
	"""获取嵌套属性值，支持点号分隔的路径"""
	var parts = property_path.split(".")
	var current = obj

	for part in parts:
		if current is Dictionary:
			if part in current:
				current = current[part]
			else:
				return null
		elif current is Object and current.has_method("get"):
			current = current.get(part)
		elif current is Object and part in current:
			current = current[part]
		else:
			return null

	return current

# 字典工具
static func merge_dictionaries(dict1: Dictionary, dict2: Dictionary) -> Dictionary:
	"""合并字典"""
	var result = dict1.duplicate()
	for key in dict2:
		result[key] = dict2[key]
	return result

static func get_nested_value(dict: Dictionary, path: String, default = null):
	"""获取嵌套字典值"""
	var keys = path.split(".")
	var current = dict
	
	for key in keys:
		if typeof(current) == TYPE_DICTIONARY and key in current:
			current = current[key]
		else:
			return default
	
	return current

static func set_nested_value(dict: Dictionary, path: String, value):
	"""设置嵌套字典值"""
	var keys = path.split(".")
	var current = dict
	
	for i in range(keys.size() - 1):
		var key = keys[i]
		if key not in current:
			current[key] = {}
		current = current[key]
	
	current[keys[-1]] = value

# 数学工具
static func clamp_int(value: int, min_val: int, max_val: int) -> int:
	"""整数限制"""
	return max(min_val, min(max_val, value))

static func lerp_safe(from: float, to: float, weight: float) -> float:
	"""安全线性插值"""
	return from + clamp(weight, 0.0, 1.0) * (to - from)

static func normalize_value(value: float, min_val: float, max_val: float) -> float:
	"""标准化值到0-1范围"""
	return (value - min_val) / (max_val - min_val) if max_val != min_val else 0.0

static func map_range(value: float, from_min: float, from_max: float, to_min: float, to_max: float) -> float:
	"""映射值范围"""
	var normalized = normalize_value(value, from_min, from_max)
	return to_min + normalized * (to_max - to_min)

# 字符串工具
static func format_time(seconds: float) -> String:
	"""格式化时间"""
	var hours = int(seconds) / 3600
	var minutes = (int(seconds) % 3600) / 60
	var secs = int(seconds) % 60
	return "%02d:%02d:%02d" % [hours, minutes, secs]

static func format_number(number: float, decimals: int = 1) -> String:
	"""格式化数字"""
	if number >= 1000000:
		return "%.1fM" % (number / 1000000.0)
	elif number >= 1000:
		return "%.1fK" % (number / 1000.0)
	else:
		return ("%."+str(decimals)+"f") % number

static func pluralize(word: String, count: int) -> String:
	"""复数化"""
	return word + ("s" if count != 1 else "")

# 颜色工具
static func get_health_color(health_percent: float) -> Color:
	"""根据健康百分比获取颜色"""
	if health_percent > 0.7:
		return Color.GREEN
	elif health_percent > 0.3:
		return Color.YELLOW
	else:
		return Color.RED

static func get_mood_color(mood: float) -> Color:
	"""根据心情获取颜色"""
	if mood > 70:
		return Color.CYAN
	elif mood > 30:
		return Color.WHITE
	else:
		return Color.ORANGE

# 概率工具
static func chance(probability: float) -> bool:
	"""概率检查"""
	return randf() < probability

static func weighted_choice(items: Array, weights: Array):
	"""加权随机选择"""
	if items.size() != weights.size() or items.is_empty():
		return null
	
	var total_weight = 0.0
	for weight in weights:
		total_weight += weight
	
	var random_value = randf() * total_weight
	var current_weight = 0.0
	
	for i in range(items.size()):
		current_weight += weights[i]
		if random_value <= current_weight:
			return items[i]
	
	return items[-1]

# 文件工具
static func save_json(data: Dictionary, file_path: String) -> bool:
	"""保存JSON文件"""
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(data))
		file.close()
		return true
	return false

static func load_json(file_path: String) -> Dictionary:
	"""加载JSON文件"""
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		if parse_result == OK:
			return json.data
	return {}

# 节点工具
#static func find_child_by_class(_node: Node, class_name: String) -> Node:
#	"""按类名查找子节点"""
#	for child in _node.get_children():
#		if child.get_script() and child.get_script().get_global_name() == class_name:
#			return child
#		var found = find_child_by_class(child, class_name)
#		if found:
#			return found
#	return null

static func get_all_children_of_type(node: Node, type: String) -> Array:
	"""获取所有指定类型的子节点"""
	var result = []
	for child in node.get_children():
		if child.get_script() and child.get_script().get_global_name() == type:
			result.append(child)
		result.append_array(get_all_children_of_type(child, type))
	return result

# 性能工具
static func measure_time(callable: Callable) -> Dictionary:
	"""测量函数执行时间"""
	var start_time = Time.get_time_dict_from_system()
	var result = callable.call()
	var end_time = Time.get_time_dict_from_system()
	
	var duration = (end_time["hour"] * 3600 + end_time["minute"] * 60 + end_time["second"]) - \
				   (start_time["hour"] * 3600 + start_time["minute"] * 60 + start_time["second"])
	
	return {
		"result": result,
		"duration": duration
	}

# 调试工具
static func debug_print(message: String, level: String = "INFO"):
	"""调试打印"""
	var timestamp = Time.get_datetime_string_from_system()
	print("[%s] [%s] %s" % [timestamp, level, message])

static func dump_object_properties(obj: Object) -> Dictionary:
	"""转储对象属性"""
	var properties = {}
	var property_list = obj.get_property_list()
	
	for property in property_list:
		var name = property["name"]
		if not name.begins_with("_"):
			properties[name] = obj.get(name)
	
	return properties

# 配置工具
class ConfigManager:
	var config_data: Dictionary = {}
	var config_file: String = ""
	
	func _init(file_path: String):
		config_file = file_path
		load_config()
	
	func load_config():
		config_data = Utils.load_json(config_file)
	
	func save_config():
		Utils.save_json(config_data, config_file)
	
	func get_value(key: String, default = null):
		return Utils.get_nested_value(config_data, key, default)
	
	func set_value(key: String, value):
		Utils.set_nested_value(config_data, key, value)
	
	func has_key(key: String) -> bool:
		return Utils.get_nested_value(config_data, key) != null

# 简单状态管理
class SimpleState:
	var data: Dictionary = {}
	var listeners: Dictionary = {}
	
	func set_state(key: String, value):
		var old_value = data.get(key)
		data[key] = value
		
		if key in listeners:
			for callback in listeners[key]:
				callback.call(value, old_value)
	
	func get_state(key: String, default = null):
		return data.get(key, default)
	
	func subscribe(key: String, callback: Callable):
		if key not in listeners:
			listeners[key] = []
		listeners[key].append(callback)
	
	func unsubscribe(key: String, callback: Callable):
		if key in listeners:
			listeners[key].erase(callback)

# 简单对象池
class SimpleObjectPool:
	var pool: Array = []
	var factory: Callable
	var reset_func: Callable
	
	func _init(p_factory: Callable, p_reset_func: Callable = Callable()):
		factory = p_factory
		reset_func = p_reset_func
	
	func get_object():
		if pool.size() > 0:
			var obj = pool.pop_back()
			return obj
		else:
			return factory.call()
	
	func return_object(obj):
		if reset_func.is_valid():
			reset_func.call(obj)
		pool.append(obj)
	
	func clear():
		pool.clear()

# 简单事件发射器
class SimpleEmitter:
	var events: Dictionary = {}
	
	func on(event_name: String, callback: Callable):
		if event_name not in events:
			events[event_name] = []
		events[event_name].append(callback)
	
	func off(event_name: String, callback: Callable):
		if event_name in events:
			events[event_name].erase(callback)
	
	func emit(event_name: String, data = null):
		if event_name in events:
			for callback in events[event_name]:
				callback.call(data)
	
	func clear():
		events.clear()
