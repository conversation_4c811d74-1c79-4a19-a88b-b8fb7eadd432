[res://scripts/UI/SimpleUI.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 12,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/UI/ResearchUI.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 83,
"scroll_position": 61.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/UI/DataVisualizationUI.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 277,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Controllers/MainSceneController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 144,
"scroll_position": 138.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Core/GameManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 157,
"scroll_position": 154.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Entities/SimpleColonist.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Systems/Social/FactionSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 120,
"scroll_position": 115.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Systems/Colonist/MedicalSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 8,
"scroll_position": 8.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Systems/Social/TradeSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 6,
"scroll_position": 6.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Systems/Environment/AnimalSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 397,
"scroll_position": 386.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Managers/DataManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 285,
"scroll_position": 285.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Utils/Utils.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 142,
"scroll_position": 142.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Test/GridSystemTestController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 11,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Test/ColonistSystemTestController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 158,
"scroll_position": 147.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Test/GameSystemsTestController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 85,
"scroll_position": 76.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Test/AllSystemsTestController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 23,
"scroll_position": 11.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Test/TestMenuController.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Entities/Animal.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 9,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 277,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
