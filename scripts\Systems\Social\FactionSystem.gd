extends Node
class_name FactionSystem

# 派系和外交系统 - 多个派系的复杂外交关系

signal faction_discovered(faction: Faction)
signal diplomatic_status_changed(faction: Faction, old_status: DiplomaticStatus, new_status: DiplomaticStatus)
signal faction_raid_incoming(faction: Faction, raid_data: Dictionary)
signal trade_agreement_signed(faction: Faction, agreement: TradeAgreement)
signal war_declared(faction: Faction)
signal peace_treaty_signed(faction: Faction)

# 外交状态
enum DiplomaticStatus {
	UNKNOWN,
	HOSTILE,
	UNFRIENDLY,
	NEUTRAL,
	FRIENDLY,
	ALLIED
}

# 派系类型
enum FactionType {
	TRIBAL,
	OUTLANDER,
	PIRATE,
	MECHANOID,
	INSECTOID,
	SPACER,
	IMPERIAL,
	CULTIST,
	CORPORATE,
	MILITARY
}

# 派系意识形态
enum Ideology {
	COLLECTIVIST,
	INDIVIDUALIST,
	SUPREMACIST,
	EGALITARIAN,
	TRANSHUMANIST,
	NATURALIST,
	MILITARIST,
	PACIFIST,
	TECHNOCRAT,
	PRIMITIVIST
}

# 贸易协议类
class TradeAgreement:
	var id: String
	var faction: Faction
	var trade_bonus: float = 1.2
	var duration: float = 86400.0  # 24小时
	var remaining_time: float
	var exclusive_goods: Array[String] = []
	
	func _init(p_faction: Faction):
		id = generate_id()
		faction = p_faction
		remaining_time = duration
	
	func generate_id() -> String:
		return "trade_agreement_" + str(randi())
	
	func update(delta: float) -> bool:
		remaining_time -= delta
		return remaining_time > 0

# 派系类
class Faction:
	var id: String
	var name: String
	var faction_type: FactionType
	var ideology: Ideology
	var leader_name: String
	var population: int
	var technology_level: int
	var military_strength: int
	var wealth: int
	var territory_size: int
	
	# 外交关系
	var diplomatic_status: DiplomaticStatus = DiplomaticStatus.UNKNOWN
	var goodwill: float = 0.0  # -100 to 100
	var reputation: float = 0.0
	var last_interaction_time: float = 0.0
	
	# 派系特性
	var traits: Array[String] = []
	var preferred_goods: Array[String] = []
	var hostile_to: Array[String] = []
	var allied_with: Array[String] = []
	
	# 派系能力
	var can_trade: bool = true
	var can_raid: bool = true
	var can_ally: bool = true
	var sends_refugees: bool = false
	var offers_quests: bool = false
	
	# 派系资源
	var resources: Dictionary = {}
	var unique_technologies: Array[String] = []
	var special_units: Array[String] = []
	
	func _init(p_name: String, p_type: FactionType, p_ideology: Ideology):
		id = generate_id()
		name = p_name
		faction_type = p_type
		ideology = p_ideology
		setup_faction_properties()
		generate_leader()
	
	func generate_id() -> String:
		return "faction_" + str(randi()) + "_" + str(GameManager.instance.game_time)
	
	func setup_faction_properties():
		var faction_type_name = FactionType.keys()[faction_type]
		var config = GameConfig.get_config("faction", faction_type_name)
		if config.is_empty():
			return

		# 应用基础属性
		technology_level = config.get("tech", 3)
		population = Utils.random_range_from_array(config.get("pop", [100, 200]))
		military_strength = Utils.random_range_from_array(config.get("military", [50, 100]))
		wealth = Utils.random_range_from_array(config.get("wealth", [500, 1000]))

		# 显式转换数组类型
		var goods_array = config.get("goods", [])
		preferred_goods.assign(goods_array)

		var traits_array = config.get("traits", [])
		traits.assign(traits_array)

		var special_array = config.get("special", [])
		unique_technologies.assign(special_array)

		var units_array = config.get("units", [])
		special_units.assign(units_array)

		# 应用标志
		var flags = config.get("flags", {})
		var flag_mappings = {
			"can_trade": "can_trade",
			"can_raid": "can_raid",
			"can_ally": "can_ally",
			"sends_refugees": "sends_refugees",
			"offers_quests": "offers_quests"
		}

		for flag in flags:
			if flag in flag_mappings:
				set(flag_mappings[flag], flags[flag])
			elif flag == "hostile" and flags[flag]:
				diplomatic_status = DiplomaticStatus.HOSTILE

		territory_size = int(population / 10.0)
		update_diplomatic_status()
	
	func generate_leader():
		var leader_names = {
			FactionType.TRIBAL: ["Chief Ironwood", "Shaman Moonwhisper", "Elder Stormcrow"],
			FactionType.OUTLANDER: ["Director Hayes", "Governor Martinez", "Captain Reynolds"],
			FactionType.PIRATE: ["Blackbeard Jake", "Scarlett the Ruthless", "Iron Fist Morgan"],
			FactionType.MECHANOID: ["Unit-Prime-Alpha", "Overseer-7734", "Core-Mind-Zero"],
			FactionType.SPACER: ["Admiral Voss", "Commander Sterling", "Captain Nova"],
			FactionType.IMPERIAL: ["Lord Regent Aurelius", "High Stellarch Maximus", "Count Valorian"]
		}

		var names = leader_names.get(faction_type, ["Unknown Leader"])
		leader_name = names.pick_random()
	
	func update_diplomatic_status():
		if goodwill >= 75:
			diplomatic_status = DiplomaticStatus.ALLIED
		elif goodwill >= 25:
			diplomatic_status = DiplomaticStatus.FRIENDLY
		elif goodwill >= -25:
			diplomatic_status = DiplomaticStatus.NEUTRAL
		elif goodwill >= -75:
			diplomatic_status = DiplomaticStatus.UNFRIENDLY
		else:
			diplomatic_status = DiplomaticStatus.HOSTILE
	
	func modify_goodwill(amount: float, _reason: String = ""):
		var old_status = diplomatic_status
		goodwill = clamp(goodwill + amount, -100.0, 100.0)
		update_diplomatic_status()

		# 如果外交状态发生变化，发出信号
		if old_status != diplomatic_status:
			if GameManager and GameManager.faction_system:
				GameManager.faction_system.diplomatic_status_changed.emit(self, old_status, diplomatic_status)
		
		if diplomatic_status != old_status:
			print("Diplomatic status with ", name, " changed from ", DiplomaticStatus.keys()[old_status], " to ", DiplomaticStatus.keys()[diplomatic_status])
		
		last_interaction_time = GameManager.instance.game_time
	
	func can_interact_diplomatically() -> bool:
		return faction_type not in [FactionType.MECHANOID] and diplomatic_status != DiplomaticStatus.UNKNOWN
	
	func get_trade_price_modifier() -> float:
		match diplomatic_status:
			DiplomaticStatus.ALLIED: return 0.8
			DiplomaticStatus.FRIENDLY: return 0.9
			DiplomaticStatus.NEUTRAL: return 1.0
			DiplomaticStatus.UNFRIENDLY: return 1.2
			DiplomaticStatus.HOSTILE: return 1.5
			_: return 1.0
	
	func get_raid_probability() -> float:
		if not can_raid:
			return 0.0
		
		match diplomatic_status:
			DiplomaticStatus.HOSTILE: return 0.3
			DiplomaticStatus.UNFRIENDLY: return 0.1
			DiplomaticStatus.NEUTRAL: return 0.02
			_: return 0.0
	
	func get_faction_info() -> Dictionary:
		return {
			"id": id,
			"name": name,
			"type": FactionType.keys()[faction_type],
			"ideology": Ideology.keys()[ideology],
			"leader": leader_name,
			"population": population,
			"technology_level": technology_level,
			"military_strength": military_strength,
			"wealth": wealth,
			"diplomatic_status": DiplomaticStatus.keys()[diplomatic_status],
			"goodwill": goodwill,
			"reputation": reputation,
			"traits": traits,
			"can_trade": can_trade,
			"can_raid": can_raid,
			"can_ally": can_ally
		}

# 外交行动类
class DiplomaticAction:
	var action_type: String
	var target_faction: Faction
	var cost: Dictionary = {}
	var success_chance: float
	var cooldown: float
	var effects: Dictionary = {}
	
	func _init(p_type: String, p_faction: Faction):
		action_type = p_type
		target_faction = p_faction
		setup_action_properties()
	
	func setup_action_properties():
		match action_type:
			"gift":
				cost = {"silver": 500}
				success_chance = 0.8
				cooldown = 86400.0  # 24小时
				effects = {"goodwill": 15.0}
			
			"trade_agreement":
				cost = {"silver": 1000}
				success_chance = 0.6
				cooldown = 172800.0  # 48小时
				effects = {"trade_bonus": 1.2}
			
			"military_aid":
				cost = {"silver": 2000, "weapons": 10}
				success_chance = 0.7
				cooldown = 259200.0  # 72小时
				effects = {"goodwill": 25.0, "military_support": true}
			
			"peace_treaty":
				cost = {"silver": 1500}
				success_chance = 0.5
				cooldown = 604800.0  # 7天
				effects = {"goodwill": 30.0, "end_hostilities": true}
			
			"alliance":
				cost = {"silver": 5000}
				success_chance = 0.3
				cooldown = 1209600.0  # 14天
				effects = {"goodwill": 50.0, "alliance": true}

# 主系统变量
var known_factions: Array[Faction] = []
var active_trade_agreements: Array[TradeAgreement] = []
var diplomatic_actions_cooldown: Dictionary = {}
var faction_discovery_timer: float = 0.0
var faction_discovery_interval: float = 1800.0  # 30分钟

func _ready():
	initialize_starting_factions()
	print("Faction System initialized")

func initialize_starting_factions():
	# 创建初始派系
	var tribal_faction = Faction.new("Rough Tribe", FactionType.TRIBAL, Ideology.COLLECTIVIST)
	var outlander_faction = Faction.new("Merchant Union", FactionType.OUTLANDER, Ideology.INDIVIDUALIST)
	var pirate_faction = Faction.new("Crimson Fleet", FactionType.PIRATE, Ideology.SUPREMACIST)
	
	# 设置初始关系
	tribal_faction.goodwill = 10.0
	outlander_faction.goodwill = 0.0
	pirate_faction.goodwill = -30.0
	
	tribal_faction.update_diplomatic_status()
	outlander_faction.update_diplomatic_status()
	pirate_faction.update_diplomatic_status()
	
	known_factions.append(tribal_faction)
	known_factions.append(outlander_faction)
	known_factions.append(pirate_faction)
	
	for faction in known_factions:
		faction_discovered.emit(faction)

func _process(delta: float):
	faction_discovery_timer += delta
	
	if faction_discovery_timer >= faction_discovery_interval:
		attempt_faction_discovery()
		faction_discovery_timer = 0.0
	
	update_trade_agreements(delta)
	update_faction_relations(delta)

func attempt_faction_discovery():
	if known_factions.size() >= 10:  # 最大派系数量
		return
	
	if randf() < 0.3:  # 30% 概率发现新派系
		discover_new_faction()

func discover_new_faction():
	var faction_types = [
		FactionType.SPACER,
		FactionType.IMPERIAL,
		FactionType.MECHANOID,
		FactionType.CULTIST,
		FactionType.CORPORATE,
		FactionType.MILITARY
	]
	
	var ideologies = [
		Ideology.TRANSHUMANIST,
		Ideology.NATURALIST,
		Ideology.MILITARIST,
		Ideology.PACIFIST,
		Ideology.TECHNOCRAT,
		Ideology.PRIMITIVIST
	]
	
	var faction_type = faction_types[randi() % faction_types.size()]
	var ideology = ideologies[randi() % ideologies.size()]
	var faction_name = generate_faction_name(faction_type)
	
	var new_faction = Faction.new(faction_name, faction_type, ideology)
	known_factions.append(new_faction)
	
	faction_discovered.emit(new_faction)
	print("Discovered new faction: ", faction_name)

func generate_faction_name(faction_type: FactionType) -> String:
	var name_parts = {
		FactionType.SPACER: ["Stellar", "Cosmic", "Void", "Nova", "Quantum"],
		FactionType.IMPERIAL: ["Royal", "Imperial", "Noble", "Regal", "Sovereign"],
		FactionType.MECHANOID: ["Cyber", "Mech", "Auto", "Synth", "Digital"],
		FactionType.CULTIST: ["Sacred", "Divine", "Holy", "Blessed", "Eternal"],
		FactionType.CORPORATE: ["Mega", "Ultra", "Prime", "Global", "Supreme"],
		FactionType.MILITARY: ["Iron", "Steel", "Thunder", "Storm", "Victory"]
	}
	
	var suffixes = {
		FactionType.SPACER: ["Consortium", "Federation", "Alliance", "Coalition"],
		FactionType.IMPERIAL: ["Empire", "Dynasty", "Dominion", "Realm"],
		FactionType.MECHANOID: ["Collective", "Network", "Hive", "Matrix"],
		FactionType.CULTIST: ["Order", "Brotherhood", "Covenant", "Circle"],
		FactionType.CORPORATE: ["Corporation", "Industries", "Enterprises", "Group"],
		FactionType.MILITARY: ["Legion", "Battalion", "Regiment", "Corps"]
	}
	
	var prefixes = name_parts.get(faction_type, ["Unknown"])
	var suffix_list = suffixes.get(faction_type, ["Faction"])
	
	var prefix = prefixes[randi() % prefixes.size()]
	var suffix = suffix_list[randi() % suffix_list.size()]
	
	return prefix + " " + suffix

func execute_diplomatic_action(action: DiplomaticAction) -> bool:
	# 检查冷却时间
	var cooldown_key = action.action_type + "_" + action.target_faction.id
	if cooldown_key in diplomatic_actions_cooldown:
		var remaining_cooldown = diplomatic_actions_cooldown[cooldown_key] - GameManager.instance.game_time
		if remaining_cooldown > 0:
			print("Diplomatic action on cooldown for ", remaining_cooldown, " seconds")
			return false
	
	# 检查资源
	for resource in action.cost:
		if GameManager.instance.get_resource(resource) < action.cost[resource]:
			print("Insufficient resources for diplomatic action")
			return false
	
	# 消耗资源
	for resource in action.cost:
		GameManager.instance.consume_resource(resource, action.cost[resource])
	
	# 执行行动
	var success = randf() < action.success_chance
	if success:
		apply_diplomatic_effects(action)
		print("Diplomatic action '", action.action_type, "' succeeded with ", action.target_faction.name)
	else:
		# 失败可能有负面后果
		action.target_faction.modify_goodwill(-5.0, "failed diplomatic action")
		print("Diplomatic action '", action.action_type, "' failed with ", action.target_faction.name)
	
	# 设置冷却时间
	diplomatic_actions_cooldown[cooldown_key] = GameManager.instance.game_time + action.cooldown
	
	return success

func apply_diplomatic_effects(action: DiplomaticAction):
	var faction = action.target_faction
	
	for effect in action.effects:
		match effect:
			"goodwill":
				faction.modify_goodwill(action.effects[effect], action.action_type)
			
			"trade_bonus":
				var agreement = TradeAgreement.new(faction)
				agreement.trade_bonus = action.effects[effect]
				active_trade_agreements.append(agreement)
				trade_agreement_signed.emit(faction, agreement)
			
			"military_support":
				# 军事支援逻辑
				pass
			
			"end_hostilities":
				if faction.diplomatic_status == DiplomaticStatus.HOSTILE:
					faction.diplomatic_status = DiplomaticStatus.UNFRIENDLY
					faction.goodwill = max(faction.goodwill, -25.0)
					peace_treaty_signed.emit(faction)
			
			"alliance":
				faction.diplomatic_status = DiplomaticStatus.ALLIED
				faction.goodwill = max(faction.goodwill, 75.0)

func update_trade_agreements(delta: float):
	for i in range(active_trade_agreements.size() - 1, -1, -1):
		var agreement = active_trade_agreements[i]
		if not agreement.update(delta):
			active_trade_agreements.remove_at(i)
			print("Trade agreement with ", agreement.faction.name, " expired")

func update_faction_relations(delta: float):
	for faction in known_factions:
		# 自然关系衰减
		var decay_rate = 0.1 * delta  # 每秒衰减0.1
		if faction.goodwill > 0:
			faction.goodwill = max(0.0, faction.goodwill - decay_rate)
		elif faction.goodwill < 0:
			faction.goodwill = min(0.0, faction.goodwill + decay_rate)
		
		# 检查袭击概率
		if randf() < faction.get_raid_probability() * delta:
			launch_faction_raid(faction)

func launch_faction_raid(faction: Faction):
	var raid_data = {
		"faction": faction,
		"raider_count": randi_range(3, 12),
		"raid_strength": faction.military_strength,
		"raid_type": choose_raid_type(faction),
		"arrival_time": GameManager.instance.game_time + randf_range(300.0, 1800.0)  # 5-30分钟
	}
	
	faction_raid_incoming.emit(faction, raid_data)
	print("Raid incoming from ", faction.name, " - ", raid_data["raider_count"], " raiders")

func choose_raid_type(faction: Faction) -> String:
	match faction.faction_type:
		FactionType.PIRATE:
			return "pillage"
		FactionType.TRIBAL:
			return "revenge"
		FactionType.MECHANOID:
			return "extermination"
		_:
			return "standard"

func get_faction_by_id(faction_id: String) -> Faction:
	for faction in known_factions:
		if faction.id == faction_id:
			return faction
	return null

func get_diplomatic_options(faction: Faction) -> Array[DiplomaticAction]:
	var options: Array[DiplomaticAction] = []
	
	if faction.can_interact_diplomatically():
		options.append(DiplomaticAction.new("gift", faction))
		
		if faction.can_trade:
			options.append(DiplomaticAction.new("trade_agreement", faction))
		
		if faction.diplomatic_status == DiplomaticStatus.HOSTILE:
			options.append(DiplomaticAction.new("peace_treaty", faction))
		elif faction.diplomatic_status == DiplomaticStatus.FRIENDLY:
			options.append(DiplomaticAction.new("alliance", faction))
		
		if faction.military_strength > 50:
			options.append(DiplomaticAction.new("military_aid", faction))
	
	return options

func get_faction_statistics() -> Dictionary:
	var stats = {
		"total_factions": known_factions.size(),
		"allied_factions": 0,
		"hostile_factions": 0,
		"active_agreements": active_trade_agreements.size(),
		"average_goodwill": 0.0
	}
	
	var total_goodwill = 0.0
	for faction in known_factions:
		total_goodwill += faction.goodwill
		
		match faction.diplomatic_status:
			DiplomaticStatus.ALLIED:
				stats["allied_factions"] += 1
			DiplomaticStatus.HOSTILE:
				stats["hostile_factions"] += 1
	
	if known_factions.size() > 0:
		stats["average_goodwill"] = total_goodwill / known_factions.size()
	
	return stats

func save_faction_data() -> Dictionary:
	var save_data = {
		"known_factions": [],
		"active_trade_agreements": [],
		"diplomatic_cooldowns": diplomatic_actions_cooldown
	}
	
	for faction in known_factions:
		save_data["known_factions"].append(faction.get_faction_info())
	
	for agreement in active_trade_agreements:
		save_data["active_trade_agreements"].append({
			"faction_id": agreement.faction.id,
			"trade_bonus": agreement.trade_bonus,
			"remaining_time": agreement.remaining_time
		})
	
	return save_data

func load_faction_data(save_data: Dictionary):
	known_factions.clear()
	active_trade_agreements.clear()
	
	if "diplomatic_cooldowns" in save_data:
		diplomatic_actions_cooldown = save_data["diplomatic_cooldowns"]
	
	# 重建派系
	if "known_factions" in save_data:
		for faction_data in save_data["known_factions"]:
			var faction = Faction.new(
				faction_data["name"],
				FactionType[faction_data["type"]],
				Ideology[faction_data["ideology"]]
			)
			faction.id = faction_data["id"]
			faction.leader_name = faction_data["leader"]
			faction.goodwill = faction_data["goodwill"]
			faction.reputation = faction_data["reputation"]
			faction.diplomatic_status = DiplomaticStatus[faction_data["diplomatic_status"]]
			known_factions.append(faction)
	
	# 重建贸易协议
	if "active_trade_agreements" in save_data:
		for agreement_data in save_data["active_trade_agreements"]:
			var faction = get_faction_by_id(agreement_data["faction_id"])
			if faction:
				var agreement = TradeAgreement.new(faction)
				agreement.trade_bonus = agreement_data["trade_bonus"]
				agreement.remaining_time = agreement_data["remaining_time"]
				active_trade_agreements.append(agreement)

func declare_war(faction: Faction, reason: String = ""):
	"""对指定派系宣战"""
	if faction.diplomatic_status == DiplomaticStatus.HOSTILE:
		print("Already at war with ", faction.name)
		return false

	# 设置为敌对状态
	faction.diplomatic_status = DiplomaticStatus.HOSTILE
	faction.goodwill = -100.0

	# 发出宣战信号
	war_declared.emit(faction)

	# 取消所有贸易协议
	var agreements_to_remove = []
	for agreement in active_trade_agreements:
		if agreement.faction == faction:
			agreements_to_remove.append(agreement)

	for agreement in agreements_to_remove:
		active_trade_agreements.erase(agreement)

	print("War declared against ", faction.name, " - Reason: ", reason)
	return true
