extends Node2D

# 游戏系统测试控制器

@onready var ui: CanvasLayer = $UI
@onready var log_text: RichTextLabel = $UI/MainPanel/HBoxContainer/RightPanel/LogContainer/LogText

# 测试状态
var test_game_manager: Node
var test_results: Dictionary = {}
var current_test: String = ""

func _ready():
	print("=== 游戏系统测试开始 ===")
	setup_test_environment()
	connect_ui_signals()
	log_message("游戏系统测试环境初始化完成", "green")

func setup_test_environment():
	"""设置测试环境"""
	# 创建测试用的GameManager
	test_game_manager = preload("res://scripts/Core/GameManager.gd").new()
	test_game_manager.name = "TestGameManager"
	add_child(test_game_manager)
	
	# 等待一帧让系统初始化
	await get_tree().process_frame
	
	log_message("GameManager初始化完成", "blue")
	log_message("所有游戏系统已加载", "blue")

func connect_ui_signals():
	"""连接UI信号"""
	var test_medical = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestMedical
	var test_combat = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestCombat
	var test_work = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestWork
	var test_mood = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestMood
	var test_research = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestResearch
	var test_trade = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestTrade
	var test_event = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestEvent
	var test_faction = $UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestFaction
	
	var run_all = $UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/RunAllTests
	var clear_log = $UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/ClearLog
	var exit_test = $UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/ExitTest
	
	test_medical.pressed.connect(_on_test_medical_pressed)
	test_combat.pressed.connect(_on_test_combat_pressed)
	test_work.pressed.connect(_on_test_work_pressed)
	test_mood.pressed.connect(_on_test_mood_pressed)
	test_research.pressed.connect(_on_test_research_pressed)
	test_trade.pressed.connect(_on_test_trade_pressed)
	test_event.pressed.connect(_on_test_event_pressed)
	test_faction.pressed.connect(_on_test_faction_pressed)
	
	run_all.pressed.connect(_on_run_all_tests_pressed)
	clear_log.pressed.connect(_on_clear_log_pressed)
	exit_test.pressed.connect(_on_exit_test_pressed)

func log_message(message: String, color: String = "white"):
	"""记录日志消息"""
	var timestamp = Time.get_datetime_string_from_system().split(" ")[1]
	var formatted_message = "[color=%s][%s] %s[/color]\n" % [color, timestamp, message]
	log_text.append_text(formatted_message)
	print(message)

func run_test(test_name: String, test_func: Callable) -> bool:
	"""运行单个测试"""
	current_test = test_name
	log_message("开始测试: " + test_name, "yellow")
	
	var success = false
	try:
		success = await test_func.call()
		if success:
			test_results[test_name] = "PASS"
			log_message("✓ " + test_name + " - 通过", "green")
		else:
			test_results[test_name] = "FAIL"
			log_message("✗ " + test_name + " - 失败", "red")
	except:
		test_results[test_name] = "ERROR"
		log_message("✗ " + test_name + " - 错误", "red")
	
	return success

# 测试方法
func test_medical_system() -> bool:
	"""测试医疗系统"""
	if not test_game_manager.medical_system:
		log_message("医疗系统未初始化", "red")
		return false
	
	# 创建测试殖民者
	var colonist = preload("res://scripts/Entities/Colonist.gd").new()
	colonist.colonist_name = "测试病人"
	test_game_manager.add_colonist(colonist)
	
	# 测试受伤
	test_game_manager.add_injury(colonist, "cut", 30.0)
	log_message("添加伤口: 切伤 (30点伤害)", "blue")
	
	# 检查伤口是否被记录
	var has_injury = test_game_manager.medical_system.has_injury(colonist)
	if has_injury:
		log_message("伤口系统正常工作", "green")
	else:
		log_message("伤口系统异常", "red")
		return false
	
	# 清理
	test_game_manager.remove_colonist(colonist)
	colonist.queue_free()
	
	return true

func test_combat_system() -> bool:
	"""测试战斗系统"""
	if not test_game_manager.combat_system:
		log_message("战斗系统未初始化", "red")
		return false
	
	# 创建两个测试战斗者
	var attacker = preload("res://scripts/Entities/Colonist.gd").new()
	var defender = preload("res://scripts/Entities/Colonist.gd").new()
	attacker.colonist_name = "攻击者"
	defender.colonist_name = "防御者"
	
	test_game_manager.add_colonist(attacker)
	test_game_manager.add_colonist(defender)
	
	# 开始战斗
	test_game_manager.start_combat(attacker, defender)
	log_message("开始战斗: 攻击者 vs 防御者", "blue")
	
	# 检查战斗是否开始
	var combat_active = test_game_manager.combat_system.has_active_combat()
	if combat_active:
		log_message("战斗系统正常工作", "green")
	else:
		log_message("战斗系统异常", "red")
		return false
	
	# 清理
	test_game_manager.remove_colonist(attacker)
	test_game_manager.remove_colonist(defender)
	attacker.queue_free()
	defender.queue_free()
	
	return true

# UI按钮回调
func _on_test_medical_pressed():
	await run_test("医疗系统", test_medical_system)

func _on_test_combat_pressed():
	await run_test("战斗系统", test_combat_system)

func _on_test_work_pressed():
	log_message("工作系统测试暂未实现", "orange")

func _on_test_mood_pressed():
	log_message("心情系统测试暂未实现", "orange")

func _on_test_research_pressed():
	log_message("研究系统测试暂未实现", "orange")

func _on_test_trade_pressed():
	log_message("贸易系统测试暂未实现", "orange")

func _on_test_event_pressed():
	log_message("事件系统测试暂未实现", "orange")

func _on_test_faction_pressed():
	log_message("派系系统测试暂未实现", "orange")

func _on_run_all_tests_pressed():
	"""运行所有测试"""
	log_message("开始运行所有测试...", "cyan")
	test_results.clear()

	var tests = [
		["医疗系统", test_medical_system],
		["战斗系统", test_combat_system]
	]

	var passed = 0
	var total = tests.size()

	for test_data in tests:
		var test_name = test_data[0]
		var test_func = test_data[1]
		var result = await run_test(test_name, test_func)
		if result:
			passed += 1

		# 等待一小段时间让UI更新
		await get_tree().create_timer(0.5).timeout

	log_message("=== 测试完成 ===", "cyan")
	log_message("通过: %d/%d" % [passed, total], "cyan")

	if passed == total:
		log_message("所有测试通过！", "green")
	else:
		log_message("部分测试失败", "red")

func _on_clear_log_pressed():
	"""清除日志"""
	log_text.clear()
	log_message("日志已清除", "blue")

func _on_exit_test_pressed():
	"""退出测试"""
	log_message("=== 游戏系统测试结束 ===", "cyan")

	var passed = 0
	var total = test_results.size()
	for result in test_results.values():
		if result == "PASS":
			passed += 1

	log_message("最终结果: %d/%d 测试通过" % [passed, total], "cyan")

	# 等待2秒后返回主场景
	await get_tree().create_timer(2.0).timeout
	get_tree().change_scene_to_file("res://scenes/Main.tscn")
