extends Node2D

# 殖民者系统测试控制器

@onready var camera: Camera2D = $Camera2D
@onready var colonists_node: Node2D = $Colonists
@onready var ui: CanvasLayer = $UI
@onready var stats_label: Label = $UI/TestPanel/VBoxContainer/StatsLabel

# 测试用场景
var colonist_scene = preload("res://scenes/SimpleColonist.tscn")
var full_colonist_scene = preload("res://scripts/Entities/Colonist.gd")

# 相机控制
var camera_speed: float = 300.0

# 测试状态
var test_colonists: Array[Node] = []
var selected_colonist: Node = null
var test_game_manager: Node

func _ready():
	print("=== 殖民者系统测试开始 ===")
	setup_test_environment()
	connect_ui_signals()
	
	# 开始统计更新
	var timer = Timer.new()
	timer.wait_time = 1.0
	timer.timeout.connect(update_stats)
	timer.autostart = true
	add_child(timer)

func setup_test_environment():
	"""设置测试环境"""
	# 创建测试用的GameManager
	test_game_manager = preload("res://scripts/Core/GameManager.gd").new()
	test_game_manager.name = "TestGameManager"
	add_child(test_game_manager)
	
	print("测试环境设置完成")
	print("控制说明:")
	print("- WASD: 移动相机")
	print("- 鼠标点击: 选择殖民者")
	print("- ESC: 退出测试")

func connect_ui_signals():
	"""连接UI信号"""
	var spawn_colonist = $UI/TestPanel/VBoxContainer/SpawnColonist
	var spawn_multiple = $UI/TestPanel/VBoxContainer/SpawnMultiple
	var test_health = $UI/TestPanel/VBoxContainer/TestHealth
	var test_mood = $UI/TestPanel/VBoxContainer/TestMood
	var test_skills = $UI/TestPanel/VBoxContainer/TestSkills
	var clear_all = $UI/TestPanel/VBoxContainer/ClearAll
	
	spawn_colonist.pressed.connect(_on_spawn_colonist_pressed)
	spawn_multiple.pressed.connect(_on_spawn_multiple_pressed)
	test_health.pressed.connect(_on_test_health_pressed)
	test_mood.pressed.connect(_on_test_mood_pressed)
	test_skills.pressed.connect(_on_test_skills_pressed)
	clear_all.pressed.connect(_on_clear_all_pressed)

func _input(event):
	handle_camera_input(event)
	handle_test_input(event)

func handle_camera_input(event):
	"""处理相机输入"""
	var camera_movement = Vector2.ZERO
	
	if Input.is_key_pressed(KEY_W):
		camera_movement.y -= 1
	if Input.is_key_pressed(KEY_S):
		camera_movement.y += 1
	if Input.is_key_pressed(KEY_A):
		camera_movement.x -= 1
	if Input.is_key_pressed(KEY_D):
		camera_movement.x += 1
	
	if camera_movement != Vector2.ZERO:
		camera.position += camera_movement.normalized() * camera_speed * get_process_delta_time()

func handle_test_input(event):
	"""处理测试输入"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_ESCAPE:
				exit_test()
	
	if event is InputEventMouseButton and event.pressed:
		if event.button_index == MOUSE_BUTTON_LEFT:
			select_colonist_at_mouse()

func spawn_colonist(use_full_colonist: bool = false):
	"""生成殖民者"""
	var colonist
	
	if use_full_colonist:
		# 使用完整的Colonist类
		colonist = full_colonist_scene.new()
		colonist.colonist_name = "测试殖民者 #" + str(test_colonists.size() + 1)
		colonist.age = randi_range(18, 65)
		colonist.gender = ["male", "female"][randi() % 2]
	else:
		# 使用简单的SimpleColonist场景
		colonist = colonist_scene.instantiate()
		colonist.colonist_name = "简单殖民者 #" + str(test_colonists.size() + 1)
	
	# 随机位置
	var spawn_pos = Vector2(
		randf_range(-200, 200),
		randf_range(-200, 200)
	)
	colonist.global_position = spawn_pos
	
	# 添加到场景
	colonists_node.add_child(colonist)
	
	# 注册到GameManager
	test_game_manager.add_colonist(colonist)
	test_colonists.append(colonist)
	
	print("殖民者已生成: ", colonist.colonist_name, " 位置: ", spawn_pos)
	return colonist

func select_colonist_at_mouse():
	"""选择鼠标位置的殖民者"""
	var mouse_pos = get_global_mouse_position()
	var closest_colonist = null
	var closest_distance = 50.0  # 最大选择距离
	
	for colonist in test_colonists:
		if is_instance_valid(colonist):
			var distance = colonist.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_colonist = colonist
	
	if closest_colonist:
		selected_colonist = closest_colonist
		print("选择了殖民者: ", selected_colonist.colonist_name)
		
		# 显示殖民者信息
		if selected_colonist.has_method("get_health"):
			print("- 健康: ", selected_colonist.get_health())
		if selected_colonist.has_method("get_mood"):
			print("- 心情: ", selected_colonist.get_mood())
	else:
		selected_colonist = null
		print("未选择任何殖民者")

func update_stats():
	"""更新统计信息"""
	var valid_colonists = []
	for colonist in test_colonists:
		if is_instance_valid(colonist):
			valid_colonists.append(colonist)
	
	test_colonists = valid_colonists
	
	if test_colonists.size() == 0:
		stats_label.text = "统计信息:\n殖民者数量: 0\n平均健康: 0%\n平均心情: 0%"
		return
	
	var total_health = 0.0
	var total_mood = 0.0
	var health_count = 0
	var mood_count = 0
	
	for colonist in test_colonists:
		if colonist.has_method("get_health"):
			total_health += colonist.get_health()
			health_count += 1
		if colonist.has_method("get_mood"):
			total_mood += colonist.get_mood()
			mood_count += 1
	
	var avg_health = total_health / max(health_count, 1)
	var avg_mood = total_mood / max(mood_count, 1)
	
	stats_label.text = "统计信息:\n殖民者数量: %d\n平均健康: %.1f%%\n平均心情: %.1f%%" % [
		test_colonists.size(),
		avg_health,
		avg_mood
	]

# UI按钮回调
func _on_spawn_colonist_pressed():
	spawn_colonist(false)

func _on_spawn_multiple_pressed():
	for i in range(5):
		spawn_colonist(i % 2 == 0)  # 交替使用简单和完整殖民者

func _on_test_health_pressed():
	"""测试健康系统"""
	if selected_colonist and selected_colonist.has_method("take_damage"):
		selected_colonist.take_damage(20.0)
		print("对选中殖民者造成20点伤害")
	elif test_colonists.size() > 0:
		var colonist = test_colonists[0]
		if colonist.has_method("take_damage"):
			colonist.take_damage(20.0)
			print("对第一个殖民者造成20点伤害")
	else:
		print("没有殖民者可以测试")

func _on_test_mood_pressed():
	"""测试心情系统"""
	if selected_colonist:
		if selected_colonist.has_method("modify_mood"):
			selected_colonist.modify_mood(randf_range(-20, 20))
			print("修改选中殖民者的心情")
		elif "mood" in selected_colonist:
			selected_colonist.mood += randf_range(-20, 20)
			selected_colonist.mood = clamp(selected_colonist.mood, 0, 100)
			print("修改选中殖民者的心情")
	else:
		print("请先选择一个殖民者")

func _on_test_skills_pressed():
	"""测试技能系统"""
	if selected_colonist and selected_colonist.has_method("improve_skill"):
		var skills = ["mining", "construction", "shooting", "medicine", "cooking"]
		var skill = skills[randi() % skills.size()]
		selected_colonist.improve_skill(skill, 1)
		print("提升选中殖民者的", skill, "技能")
	else:
		print("选中的殖民者不支持技能系统")

func _on_clear_all_pressed():
	"""清除所有殖民者"""
	for colonist in test_colonists:
		if is_instance_valid(colonist):
			test_game_manager.remove_colonist(colonist)
			colonist.queue_free()
	
	test_colonists.clear()
	selected_colonist = null
	print("所有殖民者已清除")

func exit_test():
	"""退出测试"""
	print("=== 殖民者系统测试结束 ===")
	print("测试结果:")
	print("- 测试的殖民者数量: ", test_colonists.size())
	print("- 殖民者系统运行正常: ", test_game_manager != null)
	
	get_tree().change_scene_to_file("res://scenes/Main.tscn")
