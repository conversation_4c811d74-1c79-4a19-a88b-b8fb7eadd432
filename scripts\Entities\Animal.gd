extends CharacterBody2D
class_name Animal

# 动物实体类

# 基本属性
@export var animal_name: String = "未命名动物"
@export var species: String = "unknown"  # 物种
@export var age: int = 1  # 年龄
@export var gender: String = "unknown"  # 性别

# 生存属性
@export var health: float = 100.0
@export var max_health: float = 100.0
@export var hunger: float = 0.0  # 饥饿度 (0-100)
@export var thirst: float = 0.0  # 口渴度 (0-100)

# 行为属性
@export var is_wild: bool = true  # 是否为野生动物
@export var is_tamed: bool = false  # 是否被驯服
@export var tameness: float = 0.0  # 驯服度 (0-100)
@export var aggression: float = 50.0  # 攻击性 (0-100)

# 移动属性
@export var move_speed: float = 100.0
@export var wander_radius: float = 200.0  # 游荡半径

# 所有者和派系
var animal_owner: Node = null  # 避免与Node.owner冲突
var faction: String = "wild"

# AI状态
enum AnimalState {
	IDLE,
	WANDERING,
	FLEEING,
	HUNTING,
	EATING,
	SLEEPING,
	FOLLOWING
}

var current_state: AnimalState = AnimalState.IDLE
var target_position: Vector2 = Vector2.ZERO
var home_position: Vector2 = Vector2.ZERO
var state_timer: float = 0.0

# 视觉组件
@onready var sprite: Sprite2D = $Sprite2D
@onready var collision: CollisionShape2D = $CollisionShape2D

# AI组件
var ai_timer: float = 0.0
var ai_update_interval: float = 1.0  # AI更新间隔

func _ready():
	# 设置初始位置为家位置
	home_position = global_position
	target_position = global_position
	
	# 根据物种设置默认属性
	setup_species_defaults()
	
	# 创建基本的视觉表示
	setup_visual_representation()
	
	print("动物已创建: ", animal_name, " (", species, ")")

func setup_species_defaults():
	"""根据物种设置默认属性"""
	match species:
		"rabbit":
			max_health = 30.0
			move_speed = 150.0
			aggression = 10.0
			wander_radius = 100.0
		"deer":
			max_health = 80.0
			move_speed = 200.0
			aggression = 20.0
			wander_radius = 300.0
		"bear":
			max_health = 200.0
			move_speed = 80.0
			aggression = 80.0
			wander_radius = 400.0
		"wolf":
			max_health = 120.0
			move_speed = 180.0
			aggression = 90.0
			wander_radius = 500.0
		_:
			# 默认属性
			max_health = 100.0
			move_speed = 100.0
			aggression = 50.0
			wander_radius = 200.0
	
	health = max_health

func setup_visual_representation():
	"""设置视觉表示"""
	if not sprite:
		sprite = Sprite2D.new()
		add_child(sprite)
	
	if not collision:
		collision = CollisionShape2D.new()
		var shape = CircleShape2D.new()
		shape.radius = 16.0
		collision.shape = shape
		add_child(collision)
	
	# 创建简单的颜色方块表示不同动物
	var texture = ImageTexture.new()
	var image = Image.create(32, 32, false, Image.FORMAT_RGB8)
	
	var color = Color.WHITE
	match species:
		"rabbit":
			color = Color.BROWN
		"deer":
			color = Color.SADDLE_BROWN
		"bear":
			color = Color.DARK_GRAY
		"wolf":
			color = Color.GRAY
		_:
			color = Color.WHITE
	
	image.fill(color)
	texture.set_image(image)
	sprite.texture = texture

func _physics_process(delta):
	# 更新AI
	ai_timer += delta
	if ai_timer >= ai_update_interval:
		update_ai()
		ai_timer = 0.0
	
	# 更新状态计时器
	state_timer += delta
	
	# 执行当前状态的行为
	execute_current_state(delta)
	
	# 更新生存需求
	update_survival_needs(delta)

func update_ai():
	"""更新AI决策"""
	# 简单的状态机AI
	match current_state:
		AnimalState.IDLE:
			# 随机决定是否开始游荡
			if randf() < 0.3:
				start_wandering()
		
		AnimalState.WANDERING:
			# 检查是否到达目标或需要新目标
			if global_position.distance_to(target_position) < 20.0 or state_timer > 10.0:
				if randf() < 0.5:
					set_state(AnimalState.IDLE)
				else:
					start_wandering()
		
		AnimalState.FLEEING:
			# 逃跑一段时间后回到游荡状态
			if state_timer > 5.0:
				set_state(AnimalState.WANDERING)

func execute_current_state(_delta):
	"""执行当前状态的行为"""
	match current_state:
		AnimalState.IDLE:
			# 空闲状态，不移动
			velocity = Vector2.ZERO
		
		AnimalState.WANDERING:
			# 向目标位置移动
			var direction = (target_position - global_position).normalized()
			velocity = direction * move_speed
		
		AnimalState.FLEEING:
			# 逃离威胁
			var direction = (target_position - global_position).normalized()
			velocity = direction * move_speed * 1.5  # 逃跑时速度更快
		
		_:
			velocity = Vector2.ZERO
	
	# 应用移动
	move_and_slide()

func update_survival_needs(delta):
	"""更新生存需求"""
	# 饥饿度和口渴度随时间增加
	hunger += delta * 2.0  # 每秒增加2点饥饿
	thirst += delta * 3.0  # 每秒增加3点口渴
	
	# 限制在0-100范围内
	hunger = clamp(hunger, 0.0, 100.0)
	thirst = clamp(thirst, 0.0, 100.0)
	
	# 如果饥饿或口渴过高，减少健康
	if hunger > 80.0 or thirst > 80.0:
		take_damage(delta * 5.0)

func start_wandering():
	"""开始游荡"""
	# 在游荡半径内选择随机目标
	var angle = randf() * TAU
	var distance = randf() * wander_radius
	target_position = home_position + Vector2(cos(angle), sin(angle)) * distance
	
	set_state(AnimalState.WANDERING)

func set_state(new_state: AnimalState):
	"""设置新状态"""
	current_state = new_state
	state_timer = 0.0

func take_damage(amount: float):
	"""受到伤害"""
	health -= amount
	health = max(0.0, health)
	
	if health <= 0.0:
		die()
	elif health < max_health * 0.3:
		# 健康较低时进入逃跑状态
		start_fleeing()

func heal(amount: float):
	"""治疗"""
	health += amount
	health = min(max_health, health)

func start_fleeing():
	"""开始逃跑"""
	# 选择远离当前位置的逃跑目标
	var flee_direction = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized()
	target_position = global_position + flee_direction * wander_radius * 2.0
	
	set_state(AnimalState.FLEEING)

func die():
	"""死亡"""
	print("动物死亡: ", animal_name)
	# 这里可以添加死亡效果、掉落物品等
	queue_free()

func tame(tamer: Node, taming_amount: float):
	"""驯服尝试"""
	if is_tamed:
		return true
	
	tameness += taming_amount
	tameness = clamp(tameness, 0.0, 100.0)
	
	if tameness >= 100.0:
		is_tamed = true
		is_wild = false
		animal_owner = tamer
		faction = "player"
		print("动物已被驯服: ", animal_name)
		return true
	
	return false

func get_health_percentage() -> float:
	"""获取健康百分比"""
	return (health / max_health) * 100.0

func get_info() -> Dictionary:
	"""获取动物信息"""
	return {
		"name": animal_name,
		"species": species,
		"age": age,
		"gender": gender,
		"health": health,
		"max_health": max_health,
		"health_percentage": get_health_percentage(),
		"hunger": hunger,
		"thirst": thirst,
		"is_wild": is_wild,
		"is_tamed": is_tamed,
		"tameness": tameness,
		"aggression": aggression,
		"state": AnimalState.keys()[current_state]
	}
