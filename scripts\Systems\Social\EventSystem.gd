class_name EventSystem
extends Node

# 事件系统
# 管理游戏事件

# 事件信号
signal event_triggered(event_data: Dictionary)
signal trader_arrived(trader_data: Dictionary)
signal weather_changed(weather_type: String)
signal raid_started(raid_data: Dictionary)

enum EventType {
	RAID,
	TRADER_ARRIVAL,
	WEATHER_CHANGE,
	DISEASE_OUTBREAK,
	RESOURCE_DISCOVERY,
	COLONIST_JOINS,
	ANIMAL_MIGRATION,
	SOLAR_FLARE,
	EARTHQUAKE,
	FIRE,
	RESOURCE_DROP
}

var event_timer: Timer
var last_event_time: float = 0.0

func _ready():
	name = "EventSystem"
	setup_event_timer()

func setup_event_timer():
	event_timer = Timer.new()
	event_timer.wait_time = 60.0  # 每分钟检查一次事件
	event_timer.timeout.connect(_on_event_timer_timeout)
	event_timer.autostart = true
	add_child(event_timer)

func _on_event_timer_timeout():
	# 随机触发事件
	if randf() < 0.1:  # 10% 概率触发事件
		trigger_random_event()

func trigger_random_event():
	var event_types = [EventType.TRADER_ARRIVAL, EventType.WEATHER_CHANGE, EventType.RESOURCE_DROP]
	var event_type = event_types[randi() % event_types.size()]
	trigger_event(event_type)

func trigger_event(event_type: EventType):
	var event_data = create_event_data(event_type)
	event_triggered.emit(event_data)

	# 发出特定事件信号
	match event_type:
		EventType.TRADER_ARRIVAL:
			trader_arrived.emit(event_data)
		EventType.WEATHER_CHANGE:
			weather_changed.emit(event_data.get("weather_type", "clear"))
		EventType.RAID:
			raid_started.emit(event_data)

func create_event_data(event_type: EventType) -> Dictionary:
	match event_type:
		EventType.TRADER_ARRIVAL:
			return {
				"type": event_type,
				"trader_name": "Trader " + str(randi() % 100),
				"goods": ["food", "medicine", "steel"],
				"duration": 3600.0  # 1小时
			}
		EventType.WEATHER_CHANGE:
			var weather_types = ["clear", "rain", "storm", "fog"]
			return {
				"type": event_type,
				"weather_type": weather_types[randi() % weather_types.size()],
				"duration": 7200.0  # 2小时
			}
		EventType.RAID:
			return {
				"type": event_type,
				"raider_count": randi_range(3, 8),
				"raider_type": "pirates",
				"threat_level": randf_range(0.3, 0.8)
			}
		EventType.RESOURCE_DROP:
			var resource_types = ["food", "wood", "steel", "medicine"]
			return {
				"type": event_type,
				"resource_type": resource_types[randi() % resource_types.size()],
				"amount": randi_range(10, 50)
			}
		_:
			return {
				"type": event_type,
				"description": "Unknown event occurred"
			}
