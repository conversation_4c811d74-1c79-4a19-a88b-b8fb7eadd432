extends Node
class_name DataManager

# 通用数据管理系统 - 简化配置和数据处理

# 通用配置应用器
static func apply_config(target: Object, config: Dictionary, mappings: Dictionary = {}):
	"""
	通用配置应用函数
	target: 要配置的对象
	config: 配置数据字典
	mappings: 属性映射 {"config_key": "target_property"}
	"""
	for key in config:
		var property_name = mappings.get(key, key)
		if target.has_method("set_" + property_name):
			target.call("set_" + property_name, config[key])
		elif property_name in target:
			target.set(property_name, config[key])

# 随机范围生成器
static func random_range_from_array(range_array: Array):
	"""从[min, max]数组生成随机数"""
	if range_array.size() >= 2:
		return randi_range(range_array[0], range_array[1])
	return range_array[0] if range_array.size() > 0 else 0

# 加权随机选择
static func weighted_random_choice(options: Array, weights: Array):
	"""根据权重随机选择选项"""
	if options.size() != weights.size() or options.is_empty():
		return null
	
	var total_weight = weights.reduce(func(sum, weight): return sum + weight, 0.0)
	var random_value = randf() * total_weight
	var current_weight = 0.0
	
	for i in range(options.size()):
		current_weight += weights[i]
		if random_value <= current_weight:
			return options[i]
	
	return options[-1]

# 批量创建对象
static func create_objects_from_configs(configs: Dictionary, creator_func: Callable) -> Array:
	"""从配置批量创建对象"""
	var objects = []
	for config_name in configs:
		var obj = creator_func.call(config_name, configs[config_name])
		if obj:
			objects.append(obj)
	return objects

# 条件过滤器
static func filter_by_conditions(items: Array, conditions: Dictionary) -> Array:
	"""根据条件过滤数组"""
	return items.filter(func(item): return meets_conditions(item, conditions))

static func meets_conditions(item: Object, conditions: Dictionary) -> bool:
	"""检查对象是否满足条件"""
	for condition in conditions:
		var value = get_nested_property(item, condition)
		var expected = conditions[condition]
		
		if typeof(expected) == TYPE_DICTIONARY:
			# 支持范围条件 {"min": 10, "max": 20}
			if "min" in expected and value < expected["min"]:
				return false
			if "max" in expected and value > expected["max"]:
				return false
		elif value != expected:
			return false
	
	return true

# 嵌套属性获取
static func get_nested_property(obj: Object, property_path: String):
	"""获取嵌套属性，支持点号分隔"""
	var parts = property_path.split(".")
	var current = obj
	
	for part in parts:
		if current.has_method("get_" + part):
			current = current.call("get_" + part)
		elif part in current:
			current = current.get(part)
		else:
			return null
	
	return current

# 批量更新系统
class BatchUpdater:
	var update_functions: Array[Callable] = []
	var update_intervals: Array[float] = []
	var timers: Array[float] = []

	func add_update_function(function: Callable, interval: float):
		"""添加需要定期更新的函数"""
		update_functions.append(function)
		update_intervals.append(interval)
		timers.append(0.0)

	func remove_update_function(function: Callable):
		"""移除更新函数"""
		var index = update_functions.find(function)
		if index >= 0:
			update_functions.remove_at(index)
			update_intervals.remove_at(index)
			timers.remove_at(index)

	func update(delta: float):
		for i in range(update_functions.size()):
			timers[i] += delta
			if timers[i] >= update_intervals[i]:
				update_functions[i].call(delta)
				timers[i] = 0.0

# 事件系统简化
class SimpleEventBus:
	var listeners: Dictionary = {}
	
	func subscribe(event_name: String, callback: Callable):
		if event_name not in listeners:
			listeners[event_name] = []
		listeners[event_name].append(callback)
	
	func emit(event_name: String, data = null):
		if event_name in listeners:
			for callback in listeners[event_name]:
				callback.call(data)
	
	func unsubscribe(event_name: String, callback: Callable):
		if event_name in listeners:
			listeners[event_name].erase(callback)

# 状态机简化
class SimpleStateMachine:
	var current_state: String = ""
	var states: Dictionary = {}
	var transitions: Dictionary = {}
	
	func add_state(state_name: String, enter_func: Callable = Callable(), exit_func: Callable = Callable(), update_func: Callable = Callable()):
		states[state_name] = {
			"enter": enter_func,
			"exit": exit_func,
			"update": update_func
		}
	
	func add_transition(from_state: String, to_state: String, condition: Callable = Callable()):
		if from_state not in transitions:
			transitions[from_state] = []
		transitions[from_state].append({"to": to_state, "condition": condition})
	
	func change_state(new_state: String):
		if new_state not in states:
			return false
		
		if current_state in states and states[current_state]["exit"].is_valid():
			states[current_state]["exit"].call()
		
		current_state = new_state
		if states[current_state]["enter"].is_valid():
			states[current_state]["enter"].call()
		
		return true
	
	func update(delta: float):
		if current_state in states and states[current_state]["update"].is_valid():
			states[current_state]["update"].call(delta)
		
		# 检查转换条件
		if current_state in transitions:
			for transition in transitions[current_state]:
				if not transition["condition"].is_valid() or transition["condition"].call():
					change_state(transition["to"])
					break

# 资源池管理
class ResourcePool:
	var pools: Dictionary = {}
	
	func create_pool(pool_name: String, factory_func: Callable, initial_size: int = 10):
		pools[pool_name] = {
			"factory": factory_func,
			"available": [],
			"in_use": []
		}
		
		# 预创建对象
		for i in range(initial_size):
			var obj = factory_func.call()
			pools[pool_name]["available"].append(obj)
	
	func get_object(pool_name: String):
		if pool_name not in pools:
			return null
		
		var pool = pools[pool_name]
		var obj = null
		
		if pool["available"].size() > 0:
			obj = pool["available"].pop_back()
		else:
			obj = pool["factory"].call()
		
		pool["in_use"].append(obj)
		return obj
	
	func return_object(pool_name: String, obj):
		if pool_name not in pools:
			return
		
		var pool = pools[pool_name]
		if obj in pool["in_use"]:
			pool["in_use"].erase(obj)
			pool["available"].append(obj)
			
			# 重置对象状态
			if obj.has_method("reset"):
				obj.reset()

# 配置验证器
static func validate_config(config: Dictionary, schema: Dictionary) -> Dictionary:
	"""
	验证配置是否符合模式
	返回 {"valid": bool, "errors": Array}
	"""
	var result = {"valid": true, "errors": []}
	
	# 检查必需字段
	for required_field in schema.get("required", []):
		if required_field not in config:
			result["valid"] = false
			result["errors"].append("Missing required field: " + required_field)
	
	# 检查字段类型
	var field_types = schema.get("types", {})
	for field in field_types:
		if field in config:
			var expected_type = field_types[field]
			var actual_type = typeof(config[field])
			if actual_type != expected_type:
				result["valid"] = false
				result["errors"].append("Field '%s' should be type %d, got %d" % [field, expected_type, actual_type])
	
	return result

# 性能监控器
class PerformanceMonitor:
	var timers: Dictionary = {}
	var counters: Dictionary = {}
	
	func start_timer(name: String):
		timers[name] = Time.get_time_dict_from_system()
	
	func end_timer(name: String) -> float:
		if name not in timers:
			return 0.0
		
		var start_time = timers[name]
		var end_time = Time.get_time_dict_from_system()
		var duration = (end_time["hour"] * 3600 + end_time["minute"] * 60 + end_time["second"]) - \
					   (start_time["hour"] * 3600 + start_time["minute"] * 60 + start_time["second"])
		
		timers.erase(name)
		return duration
	
	func increment_counter(name: String, amount: int = 1):
		counters[name] = counters.get(name, 0) + amount
	
	func get_stats() -> Dictionary:
		return {
			"active_timers": timers.keys(),
			"counters": counters
		}

# 数据序列化助手
static func serialize_object(obj: Object, include_private: bool = false) -> Dictionary:
	"""将对象序列化为字典"""
	var data = {}
	var property_list = obj.get_property_list()
	
	for property in property_list:
		var name = property["name"]
		if not include_private and name.begins_with("_"):
			continue
		
		var value = obj.get(name)
		if typeof(value) in [TYPE_INT, TYPE_FLOAT, TYPE_STRING, TYPE_BOOL]:
			data[name] = value
		elif typeof(value) == TYPE_ARRAY:
			data[name] = serialize_array(value)
		elif typeof(value) == TYPE_DICTIONARY:
			data[name] = value
	
	return data

static func serialize_array(arr: Array) -> Array:
	"""序列化数组"""
	var result = []
	for item in arr:
		if typeof(item) == TYPE_OBJECT and item.has_method("serialize"):
			result.append(item.serialize())
		elif typeof(item) in [TYPE_INT, TYPE_FLOAT, TYPE_STRING, TYPE_BOOL]:
			result.append(item)
	return result

# 缓存系统
class SimpleCache:
	var cache: Dictionary = {}
	var max_size: int = 100
	var access_order: Array = []
	
	func _init(p_max_size: int = 100):
		max_size = p_max_size
	
	func get_key(key: String):
		if key in cache:
			# 更新访问顺序
			access_order.erase(key)
			access_order.append(key)
			return cache[key]
		return null
	
	func set_key(key: String, value):
		if key in cache:
			cache[key] = value
			access_order.erase(key)
			access_order.append(key)
		else:
			# 检查缓存大小
			if cache.size() >= max_size:
				var oldest_key = access_order.pop_front()
				cache.erase(oldest_key)
			
			cache[key] = value
			access_order.append(key)
	
	func clear():
		cache.clear()
		access_order.clear()

# 全局实例
static var event_bus: SimpleEventBus = SimpleEventBus.new()
static var performance_monitor: PerformanceMonitor = PerformanceMonitor.new()
static var global_cache: SimpleCache = SimpleCache.new(200)
