extends CanvasLayer
class_name SimpleGameUI

# 简化的游戏 UI 管理器

# 游戏状态
var game_paused: bool = false
var current_time_speed: float = 1.0

# 选中的对象
var selected_colonist: Colonist = null

func _ready():
	print("SimpleGameUI initialized")

func _process(delta):
	# 简单的UI更新逻辑
	pass

# 简单的通知系统
func show_notification(message: String):
	print("Notification: ", message)

# 语言切换支持
func update_ui_language():
	if LocalizationManager:
		print("UI language updated")

# 调试功能
func spawn_test_colonist():
	if GameManager.instance:
		print("Spawning test colonist")

func add_test_resources():
	if GameManager.instance:
		print("Adding test resources")

# 时间控制
func toggle_pause():
	game_paused = !game_paused
	print("Game paused: ", game_paused)

func set_time_speed(speed: float):
	current_time_speed = speed
	print("Time speed set to: ", speed)

# 选择系统
func select_colonist(colonist: Colonist):
	selected_colonist = colonist
	print("Selected colonist: ", colonist.colonist_name if colonist else "None")

func deselect_colonist():
	selected_colonist = null
	print("Deselected colonist")

# 资源显示更新
func update_resource_display():
	# 简单的资源显示更新
	pass

# 殖民者UI更新
func add_colonist_to_ui(colonist: Colonist):
	print("Added colonist to UI: ", colonist.colonist_name)

# 建筑系统
func enter_building_mode(building_type: String):
	print("Entered building mode: ", building_type)

func exit_building_mode():
	print("Exited building mode")

# 菜单系统
func show_main_menu():
	print("Showing main menu")

func show_settings():
	print("Showing settings")

# 错误处理
func handle_error(error_message: String):
	print("UI Error: ", error_message)
