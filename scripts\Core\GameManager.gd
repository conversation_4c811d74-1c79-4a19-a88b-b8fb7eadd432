extends Node

# 游戏管理器 - 类似 RimWorld 的核心管理系统
# 注意：此脚本作为自动加载单例，无需使用 class_name

signal resource_changed(resource_type: String, amount: int)
signal colonist_added(colonist: Node)
signal building_built(building: Building)

# 资源系统
var resources = {
	"food": 100,
	"wood": 50,
	"stone": 30,
	"steel": 10,
	"medicine": 5
}

# 游戏对象列表
var colonists: Array[Node] = []
var buildings: Array[Building] = []
var world_map: WorldMap

# 建筑网格系统
var building_grid: Dictionary = {}  # Vector2i -> Building
const GRID_SIZE: int = 32

# 时间系统
var game_time: float = 0.0
var time_speed: float = 1.0
var paused: bool = false

# 新系统引用
var event_system: EventSystem
var research_system: ResearchSystem
var trade_system: TradeSystem
var animal_system: AnimalSystem
var power_system: PowerSystem
# 系统管理器 - 统一管理所有高级系统
var system_manager: SystemManager

# 内容管理器 - 管理动态生成的游戏内容
var content_manager: ContentManager

# 保留一些核心系统的直接引用以便快速访问
var equipment_system: EquipmentSystem
var mood_system: MoodSystem
var medical_system: MedicalSystem
var combat_system: CombatSystem
var work_system: WorkSystem
var room_system: RoomSystem
var season_system: SeasonSystem
var storyteller_system: StorytellerSystem
var faction_system: FactionSystem
var vehicle_system: VehicleSystem
var pollution_system: PollutionSystem
var religion_system: ReligionSystem
var genetic_system: GeneticSystem
var ai_system: AISystem


# 单例实例
static var instance: Node

func _ready():
	instance = self
	initialize_systems()
	print("GameManager initialized")

func initialize_systems():
	# 创建并初始化所有系统
	event_system = EventSystem.new()
	research_system = ResearchSystem.new()
	trade_system = TradeSystem.new()
	animal_system = AnimalSystem.new()
	power_system = PowerSystem.new()
	equipment_system = EquipmentSystem.new()
	mood_system = MoodSystem.new()
	medical_system = MedicalSystem.new()
	combat_system = CombatSystem.new()
	work_system = WorkSystem.new()
	room_system = RoomSystem.new()
	season_system = SeasonSystem.new()
	storyteller_system = StorytellerSystem.new()
	faction_system = FactionSystem.new()
	vehicle_system = VehicleSystem.new()
	pollution_system = PollutionSystem.new()
	religion_system = ReligionSystem.new()
	genetic_system = GeneticSystem.new()
	ai_system = AISystem.new()

	# 初始化内容管理器
	content_manager = ContentManager.new()

	# LocalizationManager 现在是自动加载单例，无需手动创建
	# 可以直接通过 LocalizationManager 访问

	# 添加系统到场景树
	add_child(event_system)
	add_child(research_system)
	add_child(trade_system)
	add_child(animal_system)
	add_child(power_system)
	add_child(equipment_system)
	add_child(mood_system)
	add_child(medical_system)
	add_child(combat_system)
	add_child(work_system)
	add_child(room_system)
	add_child(season_system)
	add_child(storyteller_system)
	add_child(faction_system)
	add_child(vehicle_system)
	add_child(pollution_system)
	add_child(religion_system)
	add_child(genetic_system)
	add_child(ai_system)
	add_child(content_manager)

	# 连接系统信号
	connect_system_signals()

func _process(delta):
	if not paused:
		game_time += delta * time_speed
		update_game_systems(delta)

func connect_system_signals():
	# 连接事件系统信号
	if event_system:
		event_system.trader_arrived.connect(_on_trader_arrived)
		event_system.weather_changed.connect(_on_weather_changed)
		event_system.raid_started.connect(_on_raid_started)

	# 连接研究系统信号
	if research_system:
		research_system.research_completed.connect(_on_research_completed)

	# 连接贸易系统信号
	if trade_system:
		trade_system.trade_completed.connect(_on_trade_completed)

func update_game_systems(delta: float):
	# 更新殖民者
	for colonist in colonists:
		if colonist and is_instance_valid(colonist):
			if colonist.has_method("update_ai"):
				colonist.update_ai(delta)

	# 更新建筑
	for building in buildings:
		if building and is_instance_valid(building):
			if building.has_method("update_building"):
				building.update_building(delta)

	# 注意：大多数系统都继承自Node，它们的_process()方法会自动被调用
	# 这里只需要手动更新一些特殊的系统方法
	if trade_system and trade_system.has_method("update_traders"):
		trade_system.update_traders(delta)
	if animal_system and animal_system.has_method("update_animals"):
		animal_system.update_animals(delta)

# 资源管理
func add_resource(type: String, amount: int):
	if type in resources:
		resources[type] += amount
		resource_changed.emit(type, resources[type])
		print("Added ", amount, " ", type, ". Total: ", resources[type])

func consume_resource(type: String, amount: int) -> bool:
	if type in resources and resources[type] >= amount:
		resources[type] -= amount
		resource_changed.emit(type, resources[type])
		print("Consumed ", amount, " ", type, ". Remaining: ", resources[type])
		return true
	return false

func get_resource(type: String) -> int:
	return resources.get(type, 0)

# 殖民者管理
func add_colonist(colonist: Node):
	colonists.append(colonist)

	# 注册到各个系统（只有完整的Colonist类才注册到复杂系统）
	if colonist is Colonist:
		if mood_system:
			mood_system.register_colonist(colonist)
		if medical_system:
			medical_system.register_colonist(colonist)
		if combat_system:
			combat_system.register_combatant(colonist)
		if work_system:
			work_system.register_colonist(colonist)
		if genetic_system:
			genetic_system.register_colonist(colonist)

	colonist_added.emit(colonist)
	var colonist_name = colonist.colonist_name if "colonist_name" in colonist else "Unknown"
	print("New colonist added: ", colonist_name)

func remove_colonist(colonist: Node):
	colonists.erase(colonist)

	# 从各个系统注销（只有完整的Colonist类才从复杂系统注销）
	if colonist is Colonist:
		if mood_system:
			mood_system.unregister_colonist(colonist)
		if combat_system:
			combat_system.unregister_combatant(colonist)
		if work_system:
			work_system.unregister_colonist(colonist)

	var colonist_name = colonist.colonist_name if "colonist_name" in colonist else "Unknown"
	print("Colonist removed: ", colonist_name)

# 建筑管理
func add_building(building: Building):
	# 确保建筑对齐到网格
	building.snap_to_grid()

	# 检查网格位置是否可用
	var occupied_cells = building.get_occupied_grid_cells()
	for cell in occupied_cells:
		if building_grid.has(cell):
			print("Warning: Building grid position occupied: ", cell)
			return false

	# 占用网格位置
	for cell in occupied_cells:
		building_grid[cell] = building

	buildings.append(building)
	building_built.emit(building)
	print("Building built: ", building.building_name, " at grid: ", building.get_grid_position())
	return true

func remove_building(building: Building):
	# 释放网格位置
	var occupied_cells = building.get_occupied_grid_cells()
	for cell in occupied_cells:
		building_grid.erase(cell)

	buildings.erase(building)
	print("Building removed: ", building.building_name)

# 网格查询方法
func is_grid_position_free(grid_pos: Vector2i) -> bool:
	"""检查网格位置是否空闲"""
	return not building_grid.has(grid_pos)

func get_building_at_grid(grid_pos: Vector2i) -> Building:
	"""获取指定网格位置的建筑"""
	return building_grid.get(grid_pos, null)

func get_building_at_world_pos(world_pos: Vector2) -> Building:
	"""获取指定世界坐标的建筑"""
	var grid_pos = Building.world_to_grid(world_pos)
	return get_building_at_grid(grid_pos)

func can_place_building_at(grid_pos: Vector2i, building_size: Vector2i = Vector2i.ONE) -> bool:
	"""检查是否可以在指定位置放置建筑"""
	for x in range(building_size.x):
		for y in range(building_size.y):
			var check_pos = Vector2i(grid_pos.x + x, grid_pos.y + y)
			if not is_grid_position_free(check_pos):
				return false
	return true

func get_nearest_free_grid_position(target_pos: Vector2i, max_distance: int = 10) -> Vector2i:
	"""获取最近的空闲网格位置"""
	for distance in range(1, max_distance + 1):
		for x in range(-distance, distance + 1):
			for y in range(-distance, distance + 1):
				if abs(x) == distance or abs(y) == distance:  # 只检查边界
					var check_pos = Vector2i(target_pos.x + x, target_pos.y + y)
					if is_grid_position_free(check_pos):
						return check_pos
	return target_pos  # 如果找不到，返回原位置

# 时间控制
func pause_game():
	paused = true
	print("Game paused")

func resume_game():
	paused = false
	print("Game resumed")

func set_time_speed(speed: float):
	time_speed = clamp(speed, 0.0, 4.0)
	print("Time speed set to: ", time_speed)

# 保存/加载系统（简化版）
func save_game():
	var save_data = {
		"resources": resources,
		"game_time": game_time,
		"colonist_count": colonists.size(),
		"building_count": buildings.size()
	}
	print("Game saved: ", save_data)
	return save_data

# 系统信号处理函数
func _on_trader_arrived(trader_data: Dictionary):
	print("Trader event: ", trader_data["trader_name"], " has arrived!")

func _on_weather_changed(weather_type: String):
	print("Weather changed to: ", weather_type)
	# 应用天气效果到殖民者和建筑

func _on_raid_started(raid_data: Dictionary):
	print("Raid alert! ", raid_data["raider_count"], " ", raid_data["raider_type"], " approaching!")

func _on_research_completed(tech_id: String):
	print("Research completed: ", tech_id)

func _on_trade_completed(trader_id: String, trade_data: Dictionary):
	print("Trade completed with ", trader_id, " - Trade data: ", trade_data)

# 新系统的便捷访问方法
func start_research(tech_id: String) -> bool:
	if research_system:
		return research_system.start_research(tech_id)
	return false

func add_research_progress(amount: int):
	if research_system:
		research_system.add_research_progress(amount)

func spawn_trader():
	if trade_system:
		return trade_system.spawn_random_trader()

func create_equipment(template_id: String, quality = 1):
	if equipment_system:
		return equipment_system.create_equipment(template_id, quality)
	return null

func tame_animal(animal, colonist: Node) -> bool:
	if animal_system:
		return animal_system.tame_animal(animal, colonist)
	return false

func trigger_event(event_type):
	if event_system:
		event_system.trigger_event(event_type)

# 新系统的便捷访问方法
func add_mood_modifier(colonist: Node, modifier_id: String, modifier_name: String, effect: float, duration: float = -1.0):
	if mood_system and colonist is Colonist:
		mood_system.add_mood_modifier(colonist, modifier_id, modifier_name, effect, duration)

func add_injury(colonist: Node, injury_type, severity: float):
	if medical_system:
		medical_system.add_injury(colonist, injury_type, severity)

func start_combat(attacker, defender):
	if combat_system:
		combat_system.start_combat(attacker, defender)

func add_work_task(work_type, location: Vector2, priority = 2):
	if work_system:
		return work_system.create_task(work_type, location, priority)
	return null

func create_room(tiles: Array[Vector2i]):
	if room_system:
		return room_system.create_room_from_tiles(tiles)
	return null

func get_current_season_info() -> Dictionary:
	if season_system:
		return season_system.get_season_info()
	return {}

func force_story_event(event_type):
	if storyteller_system:
		storyteller_system.force_story_event(event_type)

# 更多新系统的便捷访问方法
func execute_diplomatic_action(action_type: String, target_faction):
	if faction_system:
		var action = faction_system.DiplomaticAction.new(action_type, target_faction)
		return faction_system.execute_diplomatic_action(action)
	return false

func construct_vehicle(blueprint_id: String, position: Vector2):
	if vehicle_system:
		return vehicle_system.construct_vehicle(blueprint_id, position)
	return null

func start_cleanup_project(zone, investment: float):
	if pollution_system:
		return pollution_system.start_cleanup_project(zone, investment)
	return false

func found_religion(founder: Node, religion_name: String, religion_type):
	if religion_system:
		return religion_system.found_religion(founder, religion_name, religion_type)
	return null

func start_genetic_experiment(experiment_name: String, target: Node, genes: Array, researcher: Node):
	if genetic_system:
		return genetic_system.start_genetic_experiment(experiment_name, target, genes, researcher)
	return null

func create_ai_unit(ai_name: String, ai_type, position: Vector2):
	if ai_system:
		return ai_system.create_ai_unit(ai_name, ai_type, position)
	return null

# 内容管理便捷方法
func generate_content(content_type: String, count: int = 1, parameters: Dictionary = {}):
	if content_manager:
		return content_manager.generate_content(content_type, count, parameters)
	return []

func get_content(content_type: String):
	if content_manager:
		return content_manager.get_content(content_type)
	return []

func get_content_manager() -> ContentManager:
	return content_manager

func get_content_statistics():
	if content_manager:
		return content_manager.get_content_statistics()
	return {}

func load_game(save_data: Dictionary):
	if "resources" in save_data:
		resources = save_data["resources"]
	if "game_time" in save_data:
		game_time = save_data["game_time"]

	# 加载各系统数据
	if research_system and "research_data" in save_data:
		research_system.load_research_data(save_data["research_data"])
	if equipment_system and "equipment_data" in save_data:
		equipment_system.load_equipment_data(save_data["equipment_data"])
	if animal_system and "animal_data" in save_data:
		animal_system.load_animal_data(save_data["animal_data"])
	if power_system and "power_data" in save_data:
		power_system.load_power_data(save_data["power_data"])
	if mood_system and "mood_data" in save_data:
		mood_system.load_mood_data(save_data["mood_data"])
	if medical_system and "medical_data" in save_data:
		medical_system.load_medical_data(save_data["medical_data"])
	if work_system and "work_data" in save_data:
		work_system.load_work_data(save_data["work_data"])
	if room_system and "room_data" in save_data:
		room_system.load_room_data(save_data["room_data"])
	if season_system and "season_data" in save_data:
		season_system.load_season_data(save_data["season_data"])
	if faction_system and "faction_data" in save_data:
		faction_system.load_faction_data(save_data["faction_data"])
	if vehicle_system and "vehicle_data" in save_data:
		vehicle_system.load_vehicle_data(save_data["vehicle_data"])
	if pollution_system and "pollution_data" in save_data:
		pollution_system.load_pollution_data(save_data["pollution_data"])
	if religion_system and "religion_data" in save_data:
		religion_system.load_religion_data(save_data["religion_data"])
	if genetic_system and "genetic_data" in save_data:
		genetic_system.load_genetic_data(save_data["genetic_data"])
	if ai_system and "ai_data" in save_data:
		ai_system.load_ai_data(save_data["ai_data"])

	print("Game loaded")
