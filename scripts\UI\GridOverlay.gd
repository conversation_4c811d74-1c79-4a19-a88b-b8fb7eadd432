extends Node2D
class_name GridOverlay

# 网格覆盖层 - 显示32x32建筑网格

const GRID_SIZE: int = 32
const GRID_COLOR: Color = Color(0.5, 0.5, 0.5, 0.3)
const OCCUPIED_COLOR: Color = Color(1.0, 0.0, 0.0, 0.5)
const FREE_COLOR: Color = Color(0.0, 1.0, 0.0, 0.3)

var show_grid: bool = false
var show_occupation: bool = false
var grid_bounds: Rect2i = Rect2i(-50, -50, 100, 100)  # 显示的网格范围

func _ready():
	z_index = -1  # 确保网格在背景显示

func _draw():
	if not show_grid:
		return
	
	draw_grid_lines()
	
	if show_occupation:
		draw_occupation_status()

func draw_grid_lines():
	"""绘制网格线"""
	var start_x = grid_bounds.position.x * GRID_SIZE
	var end_x = (grid_bounds.position.x + grid_bounds.size.x) * GRID_SIZE
	var start_y = grid_bounds.position.y * GRID_SIZE
	var end_y = (grid_bounds.position.y + grid_bounds.size.y) * GRID_SIZE
	
	# 绘制垂直线
	for x in range(grid_bounds.position.x, grid_bounds.position.x + grid_bounds.size.x + 1):
		var line_x = x * GRID_SIZE
		draw_line(
			Vector2(line_x, start_y),
			Vector2(line_x, end_y),
			GRID_COLOR,
			1.0
		)
	
	# 绘制水平线
	for y in range(grid_bounds.position.y, grid_bounds.position.y + grid_bounds.size.y + 1):
		var line_y = y * GRID_SIZE
		draw_line(
			Vector2(start_x, line_y),
			Vector2(end_x, line_y),
			GRID_COLOR,
			1.0
		)

func draw_occupation_status():
	"""绘制网格占用状态"""
	if not GameManager.instance:
		return
	
	for x in range(grid_bounds.position.x, grid_bounds.position.x + grid_bounds.size.x):
		for y in range(grid_bounds.position.y, grid_bounds.position.y + grid_bounds.size.y):
			var grid_pos = Vector2i(x, y)
			var world_pos = Vector2(x * GRID_SIZE, y * GRID_SIZE)
			var rect = Rect2(world_pos, Vector2(GRID_SIZE, GRID_SIZE))
			
			if GameManager.instance.is_grid_position_free(grid_pos):
				draw_rect(rect, FREE_COLOR)
			else:
				draw_rect(rect, OCCUPIED_COLOR)

func toggle_grid_display():
	"""切换网格显示"""
	show_grid = not show_grid
	queue_redraw()

func toggle_occupation_display():
	"""切换占用状态显示"""
	show_occupation = not show_occupation
	queue_redraw()

func set_grid_bounds(bounds: Rect2i):
	"""设置网格显示范围"""
	grid_bounds = bounds
	queue_redraw()

func update_grid_bounds_from_camera(camera: Camera2D):
	"""根据相机位置更新网格显示范围"""
	var viewport_size = get_viewport().get_visible_rect().size
	var zoom = camera.zoom.x
	var camera_pos = camera.global_position
	
	# 计算相机可见区域
	var visible_size = viewport_size / zoom
	var visible_rect = Rect2(
		camera_pos - visible_size * 0.5,
		visible_size
	)
	
	# 转换为网格坐标
	var grid_start = Vector2i(
		int(floor(visible_rect.position.x / GRID_SIZE)) - 2,
		int(floor(visible_rect.position.y / GRID_SIZE)) - 2
	)
	var grid_end = Vector2i(
		int(ceil(visible_rect.end.x / GRID_SIZE)) + 2,
		int(ceil(visible_rect.end.y / GRID_SIZE)) + 2
	)
	
	grid_bounds = Rect2i(
		grid_start,
		grid_end - grid_start
	)
	
	queue_redraw()

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_G:
				toggle_grid_display()
				print("Grid display: ", show_grid)
			KEY_H:
				toggle_occupation_display()
				print("Occupation display: ", show_occupation)
