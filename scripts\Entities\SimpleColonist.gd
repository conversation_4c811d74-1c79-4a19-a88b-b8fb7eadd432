extends CharacterBody2D
class_name SimpleColonist

# 简化的殖民者类 - 用于测试

@export var colonist_name: String = "Colonist"
@export var health: float = 100.0
@export var mood: float = 50.0

func _ready():
	print("Simple colonist created: ", colonist_name)

func _physics_process(_delta):
	# 简单的物理处理
	move_and_slide()

func take_damage(amount: float):
	health -= amount
	if health <= 0:
		queue_free()

func get_health() -> float:
	return health

func get_mood() -> float:
	return mood

func update_ai(_delta: float):
	# 简单的AI更新 - 可以在这里添加基本的行为逻辑
	pass
