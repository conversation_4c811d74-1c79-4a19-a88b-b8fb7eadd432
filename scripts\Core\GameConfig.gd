class_name GameConfig
extends RefCounted

# 游戏配置类
# 存储所有游戏配置数据和常量

# 游戏基础配置
const GAME_VERSION = "1.0.0"
const SAVE_VERSION = 1
const MAX_COLONISTS = 50
const MAX_BUILDINGS = 200
const MAX_ANIMALS = 100

# 时间配置
const SECONDS_PER_GAME_HOUR = 60.0
const HOURS_PER_DAY = 24
const DAYS_PER_SEASON = 15
const SEASONS_PER_YEAR = 4

# 资源类型
enum ResourceType {
	FOOD,
	WOOD,
	STONE,
	STEEL,
	MEDICINE,
	POWER,
	RESEARCH_POINTS
}

# 技能类型
enum SkillType {
	MINING,
	CONSTRUCTION,
	GROWING,
	COOKING,
	HUNTING,
	CRAFTING,
	RESEARCH,
	MEDICAL,
	SOCIAL,
	COMBAT
}

# 心情修正因子
const MOOD_FACTORS = {
	"comfortable_temperature": 5,
	"uncomfortable_temperature": -10,
	"good_food": 3,
	"bad_food": -5,
	"clean_environment": 2,
	"dirty_environment": -8,
	"social_interaction": 4,
	"isolation": -6,
	"injury": -15,
	"illness": -20
}

# 需求配置
const NEED_DECAY_RATES = {
	"hunger": 0.1,
	"thirst": 0.15,
	"sleep": 0.08,
	"comfort": 0.05,
	"recreation": 0.03
}

# 建筑配置
const BUILDING_CONFIGS = {
	"wall": {
		"name": "Wall",
		"cost": {"wood": 5, "stone": 2},
		"health": 200,
		"construction_time": 30
	},
	"door": {
		"name": "Door", 
		"cost": {"wood": 10},
		"health": 100,
		"construction_time": 20
	},
	"bed": {
		"name": "Bed",
		"cost": {"wood": 15, "steel": 5},
		"health": 150,
		"construction_time": 60
	},
	"research_bench": {
		"name": "Research Bench",
		"cost": {"wood": 20, "steel": 10},
		"health": 120,
		"construction_time": 90
	}
}

# 研究配置
const RESEARCH_CONFIGS = {
	"basic_construction": {
		"name": "Basic Construction",
		"cost": 100,
		"prerequisites": [],
		"unlocks": ["wall", "door"]
	},
	"furniture": {
		"name": "Furniture",
		"cost": 200,
		"prerequisites": ["basic_construction"],
		"unlocks": ["bed", "chair", "table"]
	},
	"advanced_research": {
		"name": "Advanced Research",
		"cost": 300,
		"prerequisites": ["furniture"],
		"unlocks": ["research_bench", "laboratory"]
	}
}

# 动物配置
const ANIMAL_CONFIGS = {
	"rabbit": {
		"name": "Rabbit",
		"health": 30,
		"speed": 80,
		"food_value": 15,
		"leather_value": 2
	},
	"deer": {
		"name": "Deer", 
		"health": 80,
		"speed": 120,
		"food_value": 40,
		"leather_value": 8
	},
	"bear": {
		"name": "Bear",
		"health": 200,
		"speed": 60,
		"food_value": 80,
		"leather_value": 15,
		"aggressive": true
	}
}

# 派系配置
const FACTION_CONFIGS = {
	"player": {
		"name": "Player Colony",
		"color": Color.BLUE,
		"friendly": true
	},
	"pirates": {
		"name": "Pirates",
		"color": Color.RED,
		"hostile": true,
		"raid_chance": 0.1
	},
	"traders": {
		"name": "Traders",
		"color": Color.GREEN,
		"friendly": true,
		"trade_frequency": 7
	}
}

# 事件配置
const EVENT_CONFIGS = {
	"solar_flare": {
		"name": "Solar Flare",
		"description": "A solar flare disables all electrical equipment",
		"duration": 24,
		"effects": {"power_disabled": true}
	},
	"cold_snap": {
		"name": "Cold Snap",
		"description": "Unusually cold weather affects crop growth",
		"duration": 72,
		"effects": {"temperature_modifier": -20}
	},
	"trader_arrival": {
		"name": "Trader Arrival",
		"description": "A friendly trader has arrived",
		"duration": 48,
		"effects": {"trade_available": true}
	}
}

# 获取配置的静态方法
static func get_building_config(building_type: String) -> Dictionary:
	return BUILDING_CONFIGS.get(building_type, {})

static func get_research_config(research_id: String) -> Dictionary:
	return RESEARCH_CONFIGS.get(research_id, {})

static func get_animal_config(animal_type: String) -> Dictionary:
	return ANIMAL_CONFIGS.get(animal_type, {})

static func get_faction_config(faction_id: String) -> Dictionary:
	return FACTION_CONFIGS.get(faction_id, {})

static func get_event_config(event_id: String) -> Dictionary:
	return EVENT_CONFIGS.get(event_id, {})

static func get_mood_factor(factor_name: String) -> int:
	return MOOD_FACTORS.get(factor_name, 0)

static func get_need_decay_rate(need_name: String) -> float:
	return NEED_DECAY_RATES.get(need_name, 0.1)

# 验证配置完整性
static func validate_configs() -> bool:
	# 验证建筑配置
	for building_id in BUILDING_CONFIGS:
		var config = BUILDING_CONFIGS[building_id]
		if not config.has("name") or not config.has("cost") or not config.has("health"):
			print("Invalid building config: ", building_id)
			return false
	
	# 验证研究配置
	for research_id in RESEARCH_CONFIGS:
		var config = RESEARCH_CONFIGS[research_id]
		if not config.has("name") or not config.has("cost"):
			print("Invalid research config: ", research_id)
			return false
	
	return true

# 获取配置（静态方法）
static func get_config(config_type: String = "", config_key: String = ""):
	if config_type == "":
		return {
			"factions": FACTION_CONFIGS,
			"events": EVENT_CONFIGS,
			"research": RESEARCH_CONFIGS,
			"buildings": BUILDING_CONFIGS,
			"animals": ANIMAL_CONFIGS
		}

	var config_data = {}
	match config_type:
		"faction", "factions":
			config_data = FACTION_CONFIGS
		"event", "events":
			config_data = EVENT_CONFIGS
		"research":
			config_data = RESEARCH_CONFIGS
		"building", "buildings":
			config_data = BUILDING_CONFIGS
		"animal", "animals":
			config_data = ANIMAL_CONFIGS
		"trait", "traits":
			# 添加特质配置支持
			config_data = {
				"brave": {"name": "Brave", "incompatible_with": ["coward"]},
				"coward": {"name": "Coward", "incompatible_with": ["brave"]},
				"smart": {"name": "Smart", "incompatible_with": []},
				"strong": {"name": "Strong", "incompatible_with": ["weak"]},
				"weak": {"name": "Weak", "incompatible_with": ["strong"]}
			}

	if config_key == "":
		return config_data
	else:
		return config_data.get(config_key, {})

# 获取本地化配置（静态方法）
static func get_localized_config(config_type: String, config_key: String = "") -> Dictionary:
	return get_config(config_type, config_key)
