[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Transform"), NodePath("UI/TestPanel"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/Title"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/SpawnColonist"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/SpawnMultiple"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/TestHealth"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/TestMood"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/TestSkills"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/ClearAll"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/HSeparator2"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/StatsLabel"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/HSeparator3"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/InfoLabel"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[NodePath("UI")]
