[gd_scene load_steps=2 format=3 uid="uid://ce14vylsyvpdt"]

[ext_resource type="Script" path="res://scripts/Test/TestMenuController.gd" id="1_4m5n6"]

[node name="TestMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_4m5n6")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.15, 1)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MainPanel" type="Panel" parent="CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(600, 500)

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="Title" type="Label" parent="CenterContainer/MainPanel/VBoxContainer"]
layout_mode = 2
text = "RimWorld-like 游戏测试菜单"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Subtitle" type="Label" parent="CenterContainer/MainPanel/VBoxContainer"]
layout_mode = 2
text = "选择要测试的系统或功能"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/MainPanel/VBoxContainer"]
layout_mode = 2

[node name="TestButtons" type="VBoxContainer" parent="CenterContainer/MainPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="SingleSystemTests" type="Label" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "单一系统测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2

[node name="GridSystemTest" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "网格系统测试"

[node name="ColonistSystemTest" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "殖民者系统测试"

[node name="GameSystemsTest" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "游戏系统测试"

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2

[node name="ComprehensiveTests" type="Label" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "综合测试"
horizontal_alignment = 1

[node name="HSeparator3" type="HSeparator" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2

[node name="AllSystemsTest" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2
text = "全系统综合测试"

[node name="HSeparator4" type="HSeparator" parent="CenterContainer/MainPanel/VBoxContainer/TestButtons"]
layout_mode = 2

[node name="ControlButtons" type="HBoxContainer" parent="CenterContainer/MainPanel/VBoxContainer"]
layout_mode = 2

[node name="BackToMain" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/ControlButtons"]
layout_mode = 2
size_flags_horizontal = 3
text = "返回主菜单"

[node name="ExitGame" type="Button" parent="CenterContainer/MainPanel/VBoxContainer/ControlButtons"]
layout_mode = 2
size_flags_horizontal = 3
text = "退出游戏"

[node name="InfoPanel" type="Panel" parent="."]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -300.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 400.0

[node name="VBoxContainer" type="VBoxContainer" parent="InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="InfoTitle" type="Label" parent="InfoPanel/VBoxContainer"]
layout_mode = 2
text = "测试说明"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="InfoPanel/VBoxContainer"]
layout_mode = 2

[node name="InfoText" type="RichTextLabel" parent="InfoPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[b]网格系统测试[/b]
测试32x32网格对齐建筑系统

[b]殖民者系统测试[/b]
测试殖民者生成、选择和管理

[b]游戏系统测试[/b]
测试各个游戏子系统的功能

[b]全系统综合测试[/b]
完整的游戏环境测试，包含所有功能

[color=yellow]控制说明:[/color]
- WASD: 移动相机
- 鼠标: 交互操作
- ESC: 退出测试"
