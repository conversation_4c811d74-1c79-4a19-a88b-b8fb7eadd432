extends Node2D

# 主场景控制器 - 管理游戏的主要场景逻辑

# 预加载网格覆盖层
const GridOverlayScene = preload("res://scripts/UI/GridOverlay.gd")

@onready var camera: Camera2D = $Camera2D
@onready var ui: CanvasLayer = $UI
@onready var world: Node2D = $World
@onready var colonists_node: Node2D = $World/Colonists
@onready var buildings_node: Node2D = $World/Buildings
@onready var animals_node: Node2D = $World/Animals

# 网格覆盖层
var grid_overlay: Node2D

# 游戏状态
var is_paused: bool = false
var current_speed: float = 1.0
var selected_colonist: Colonist = null
var camera_speed: float = 300.0

# 预加载的场景
var colonist_scene = preload("res://scenes/SimpleColonist.tscn")

func _ready():
	print("Main scene initialized")
	setup_initial_game_state()
	connect_ui_signals()

	# 初始化网格覆盖层
	setup_grid_overlay()

	# 生成初始殖民者
	spawn_initial_colonists()

	# 设置相机初始位置
	camera.position = Vector2.ZERO

func _input(event):
	handle_camera_input(event)
	handle_game_input(event)

func handle_camera_input(event):
	# WASD 相机移动
	var camera_movement = Vector2.ZERO
	
	if Input.is_action_pressed("ui_up") or Input.is_key_pressed(KEY_W):
		camera_movement.y -= 1
	if Input.is_action_pressed("ui_down") or Input.is_key_pressed(KEY_S):
		camera_movement.y += 1
	if Input.is_action_pressed("ui_left") or Input.is_key_pressed(KEY_A):
		camera_movement.x -= 1
	if Input.is_action_pressed("ui_right") or Input.is_key_pressed(KEY_D):
		camera_movement.x += 1
	
	if camera_movement != Vector2.ZERO:
		camera.position += camera_movement.normalized() * camera_speed * get_process_delta_time()
		# 更新网格显示范围
		if grid_overlay and grid_overlay.has_method("update_grid_bounds_from_camera"):
			grid_overlay.update_grid_bounds_from_camera(camera)
	
	# 鼠标滚轮缩放
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			camera.zoom *= 1.1
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			camera.zoom *= 0.9
		
		# 限制缩放范围
		camera.zoom = camera.zoom.clamp(Vector2(0.3, 0.3), Vector2(2.0, 2.0))

func handle_game_input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_SPACE:
				toggle_pause()
			KEY_1:
				set_game_speed(1.0)
			KEY_2:
				set_game_speed(2.0)
			KEY_3:
				set_game_speed(3.0)
			KEY_ESCAPE:
				# 取消选择
				if selected_colonist:
					deselect_colonist()
			KEY_T:
				# 快捷键打开测试菜单
				open_test_menu()

func setup_grid_overlay():
	"""初始化网格覆盖层"""
	grid_overlay = GridOverlayScene.new()
	world.add_child(grid_overlay)
	print("Grid overlay initialized. Press G to toggle grid, H to toggle occupation status")

func setup_initial_game_state():
	# 初始化游戏管理器
	if not GameManager.instance:
		var game_manager = preload("res://scripts/Core/GameManager.gd").new()
		add_child(game_manager)
	
	# 设置初始资源
	if GameManager.instance:
		GameManager.instance.resources = {
			"food": 100,
			"wood": 50,
			"stone": 30,
			"steel": 10,
			"medicine": 5
		}

func connect_ui_signals():
	# 连接UI按钮信号
	if ui:
		var ui_script = ui.get_script()
		if ui_script:
			# 时间控制按钮
			var pause_btn = ui.get_node_or_null("TopPanel/TimeContainer/PauseButton")
			var speed1_btn = ui.get_node_or_null("TopPanel/TimeContainer/Speed1Button")
			var speed2_btn = ui.get_node_or_null("TopPanel/TimeContainer/Speed2Button")
			var speed3_btn = ui.get_node_or_null("TopPanel/TimeContainer/Speed3Button")
			
			if pause_btn:
				pause_btn.pressed.connect(toggle_pause)
			if speed1_btn:
				speed1_btn.pressed.connect(func(): set_game_speed(1.0))
			if speed2_btn:
				speed2_btn.pressed.connect(func(): set_game_speed(2.0))
			if speed3_btn:
				speed3_btn.pressed.connect(func(): set_game_speed(3.0))
			
			# 功能按钮
			var build_btn = ui.get_node_or_null("BottomPanel/ButtonContainer/BuildButton")
			var research_btn = ui.get_node_or_null("BottomPanel/ButtonContainer/ResearchButton")
			var trade_btn = ui.get_node_or_null("BottomPanel/ButtonContainer/TradeButton")
			var systems_btn = ui.get_node_or_null("BottomPanel/ButtonContainer/SystemsButton")
			var language_btn = ui.get_node_or_null("BottomPanel/ButtonContainer/LanguageButton")
			
			if build_btn:
				build_btn.pressed.connect(open_build_menu)
			if research_btn:
				research_btn.pressed.connect(open_research_menu)
			if trade_btn:
				trade_btn.pressed.connect(open_trade_menu)
			if systems_btn:
				systems_btn.pressed.connect(open_systems_menu)
			if language_btn:
				language_btn.pressed.connect(open_language_menu)

func spawn_initial_colonists():
	# 生成3个初始殖民者
	for i in range(3):
		spawn_colonist(Vector2(i * 100, 0))

func spawn_colonist(spawn_pos: Vector2):
	if colonist_scene:
		var colonist = colonist_scene.instantiate()
		colonist.position = spawn_pos
		colonists_node.add_child(colonist)

		# 注册到游戏管理器
		if GameManager.instance:
			GameManager.instance.add_colonist(colonist)

		print("Spawned colonist at ", spawn_pos)

func toggle_pause():
	is_paused = !is_paused
	if GameManager.instance:
		if is_paused:
			GameManager.instance.pause_game()
		else:
			GameManager.instance.resume_game()
	
	update_ui_time_display()

func set_game_speed(speed: float):
	current_speed = speed
	if GameManager.instance:
		GameManager.instance.set_time_speed(speed)
	
	update_ui_time_display()

func update_ui_time_display():
	var time_label = ui.get_node_or_null("TopPanel/TimeContainer/TimeLabel")
	if time_label and GameManager.instance:
		var days = int(GameManager.instance.game_time / 86400)  # 86400秒 = 1天
		var speed_text = ""
		if is_paused:
			speed_text = " (暂停)"
		else:
			speed_text = " (" + str(current_speed) + "x)"
		
		time_label.text = "时间: " + str(days) + "天" + speed_text

func select_colonist(colonist: Colonist):
	if selected_colonist:
		deselect_colonist()
	
	selected_colonist = colonist
	# 添加选择视觉效果
	print("Selected colonist: ", colonist.colonist_name if colonist else "None")

func deselect_colonist():
	if selected_colonist:
		# 移除选择视觉效果
		selected_colonist = null
		print("Deselected colonist")

# UI菜单函数
func open_build_menu():
	print("Opening build menu...")
	# TODO: 实现建造菜单

	# 临时：在鼠标位置放置一个测试建筑
	place_building_at_mouse()

func place_building_at_mouse():
	"""在鼠标位置放置建筑（网格对齐）"""
	var mouse_pos = get_global_mouse_position()
	var grid_pos = Building.world_to_grid(mouse_pos)

	# 检查位置是否可用
	if not GameManager.instance.can_place_building_at(grid_pos):
		print("Cannot place building at grid position: ", grid_pos)
		return

	# 创建建筑
	var building = Building.new()
	building.building_name = "Test Wall"
	building.building_type = "wall"
	building.set_grid_position(grid_pos)

	# 添加到场景
	buildings_node.add_child(building)

	# 注册到GameManager
	if GameManager.instance.add_building(building):
		print("Building placed at grid: ", grid_pos, " world: ", building.global_position)
	else:
		building.queue_free()
		print("Failed to place building")

func open_research_menu():
	print("Opening research menu...")
	# TODO: 实现研究菜单

func open_trade_menu():
	print("Opening trade menu...")
	# TODO: 实现贸易菜单

func open_systems_menu():
	print("Opening systems menu...")
	# TODO: 实现系统菜单

func open_language_menu():
	print("Opening language menu...")
	# TODO: 实现语言菜单

func _process(_delta):
	if not is_paused:
		update_ui_time_display()
		update_resource_display()

func update_resource_display():
	if not GameManager.instance:
		return
	
	var resources = GameManager.instance.resources
	
	var food_label = ui.get_node_or_null("TopPanel/ResourceContainer/FoodLabel")
	var wood_label = ui.get_node_or_null("TopPanel/ResourceContainer/WoodLabel")
	var stone_label = ui.get_node_or_null("TopPanel/ResourceContainer/StoneLabel")
	var steel_label = ui.get_node_or_null("TopPanel/ResourceContainer/SteelLabel")
	var medicine_label = ui.get_node_or_null("TopPanel/ResourceContainer/MedicineLabel")
	
	if food_label:
		food_label.text = "🍖 食物: " + str(resources.get("food", 0))
	if wood_label:
		wood_label.text = "🪵 木材: " + str(resources.get("wood", 0))
	if stone_label:
		stone_label.text = "🪨 石材: " + str(resources.get("stone", 0))
	if steel_label:
		steel_label.text = "⚙️ 钢材: " + str(resources.get("steel", 0))
	if medicine_label:
		medicine_label.text = "💊 药品: " + str(resources.get("medicine", 0))

func open_test_menu():
	"""打开测试菜单"""
	print("打开测试菜单...")
	get_tree().change_scene_to_file("res://scenes/Test/TestMenu.tscn")
