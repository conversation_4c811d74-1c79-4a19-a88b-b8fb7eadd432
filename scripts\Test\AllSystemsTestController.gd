extends Node2D

# 全系统综合测试控制器

@onready var camera: Camera2D = $Camera2D
@onready var world: Node2D = $World
@onready var grid_overlay: Node2D = $World/GridOverlay
@onready var colonists_node: Node2D = $World/Colonists
@onready var buildings_node: Node2D = $World/Buildings
@onready var animals_node: Node2D = $World/Animals
@onready var ui: CanvasLayer = $UI

# UI元素
@onready var colonist_stats: Label = $UI/TopPanel/HBoxContainer/StatsContainer/ColonistStats
@onready var building_stats: Label = $UI/TopPanel/HBoxContainer/StatsContainer/BuildingStats
@onready var system_stats: Label = $UI/TopPanel/HBoxContainer/StatsContainer/SystemStats
@onready var log_text: RichTextLabel = $UI/RightPanel/VBoxContainer/LogContainer/LogText

# 测试场景
var colonist_scene = preload("res://scenes/SimpleColonist.tscn")
var full_colonist_scene = preload("res://scripts/Entities/Colonist.gd")
var building_scene = preload("res://scripts/Entities/Building.gd")
# Animal类将直接实例化，不需要preload

# 相机控制
var camera_speed: float = 400.0
var zoom_speed: float = 0.1
var min_zoom: float = 0.3
var max_zoom: float = 5.0

# 测试状态
var test_game_manager: Node
var test_colonists: Array[Node] = []
var test_buildings: Array[Node] = []
var test_animals: Array[Node] = []
var selected_entity: Node = null
var ui_visible: bool = true

func _ready():
	print("=== 全系统综合测试开始 ===")
	setup_test_environment()
	connect_ui_signals()
	
	# 启动统计更新定时器
	var stats_timer = Timer.new()
	stats_timer.wait_time = 1.0
	stats_timer.timeout.connect(update_stats)
	stats_timer.autostart = true
	add_child(stats_timer)
	
	# 显示网格
	if grid_overlay.has_method("toggle_grid_display"):
		grid_overlay.show_grid = true
		grid_overlay.queue_redraw()
	
	log_message("全系统测试环境初始化完成", "green")

func setup_test_environment():
	"""设置测试环境"""
	# 创建测试用的GameManager
	test_game_manager = preload("res://scripts/Core/GameManager.gd").new()
	test_game_manager.name = "TestGameManager"
	add_child(test_game_manager)
	
	log_message("GameManager初始化完成", "blue")
	log_message("控制说明:", "cyan")
	log_message("- WASD: 移动相机", "white")
	log_message("- 鼠标滚轮: 缩放", "white")
	log_message("- 鼠标左键: 选择/放置", "white")
	log_message("- 鼠标右键: 移除", "white")
	log_message("- G: 切换网格显示", "white")
	log_message("- U: 切换UI显示", "white")
	log_message("- ESC: 退出测试", "white")

func connect_ui_signals():
	"""连接UI信号"""
	var spawn_colonist_btn = $UI/LeftPanel/VBoxContainer/SpawnColonist
	var place_building_btn = $UI/LeftPanel/VBoxContainer/PlaceBuilding
	var spawn_animal_btn = $UI/LeftPanel/VBoxContainer/SpawnAnimal
	var test_raid_btn = $UI/LeftPanel/VBoxContainer/TestRaid
	var test_trade_btn = $UI/LeftPanel/VBoxContainer/TestTrade
	var test_event_btn = $UI/LeftPanel/VBoxContainer/TestEvent
	var toggle_grid_btn = $UI/LeftPanel/VBoxContainer/ToggleGrid
	var toggle_ui_btn = $UI/LeftPanel/VBoxContainer/ToggleUI
	var exit_test_btn = $UI/LeftPanel/VBoxContainer/ExitTest

	spawn_colonist_btn.pressed.connect(_on_spawn_colonist_pressed)
	place_building_btn.pressed.connect(_on_place_building_pressed)
	spawn_animal_btn.pressed.connect(_on_spawn_animal_pressed)
	test_raid_btn.pressed.connect(_on_test_raid_pressed)
	test_trade_btn.pressed.connect(_on_test_trade_pressed)
	test_event_btn.pressed.connect(_on_test_event_pressed)
	toggle_grid_btn.pressed.connect(_on_toggle_grid_pressed)
	toggle_ui_btn.pressed.connect(_on_toggle_ui_pressed)
	exit_test_btn.pressed.connect(_on_exit_test_pressed)

func _input(event):
	handle_camera_input(event)
	handle_test_input(event)

func handle_camera_input(event):
	"""处理相机输入"""
	# WASD 相机移动
	var camera_movement = Vector2.ZERO
	
	if Input.is_key_pressed(KEY_W):
		camera_movement.y -= 1
	if Input.is_key_pressed(KEY_S):
		camera_movement.y += 1
	if Input.is_key_pressed(KEY_A):
		camera_movement.x -= 1
	if Input.is_key_pressed(KEY_D):
		camera_movement.x += 1
	
	if camera_movement != Vector2.ZERO:
		camera.position += camera_movement.normalized() * camera_speed * get_process_delta_time()
		# 更新网格显示范围
		if grid_overlay.has_method("update_grid_bounds_from_camera"):
			grid_overlay.update_grid_bounds_from_camera(camera)
	
	# 鼠标滚轮缩放
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			var new_zoom = camera.zoom * (1.0 + zoom_speed)
			camera.zoom = Vector2(min(new_zoom.x, max_zoom), min(new_zoom.y, max_zoom))
			if grid_overlay.has_method("update_grid_bounds_from_camera"):
				grid_overlay.update_grid_bounds_from_camera(camera)
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			var new_zoom = camera.zoom * (1.0 - zoom_speed)
			camera.zoom = Vector2(max(new_zoom.x, min_zoom), max(new_zoom.y, min_zoom))
			if grid_overlay.has_method("update_grid_bounds_from_camera"):
				grid_overlay.update_grid_bounds_from_camera(camera)

func handle_test_input(event):
	"""处理测试输入"""
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_G:
				toggle_grid_display()
			KEY_U:
				toggle_ui_display()
			KEY_ESCAPE:
				exit_test()
	
	if event is InputEventMouseButton and event.pressed:
		match event.button_index:
			MOUSE_BUTTON_LEFT:
				select_entity_at_mouse()
			MOUSE_BUTTON_RIGHT:
				remove_entity_at_mouse()

func log_message(message: String, color: String = "white"):
	"""记录日志消息"""
	var timestamp = Time.get_datetime_string_from_system().split(" ")[1]
	var formatted_message = "[color=%s][%s] %s[/color]\n" % [color, timestamp, message]
	log_text.append_text(formatted_message)
	print(message)

func update_stats():
	"""更新统计信息"""
	# 清理无效实体
	test_colonists = test_colonists.filter(func(c): return is_instance_valid(c))
	test_buildings = test_buildings.filter(func(b): return is_instance_valid(b))
	test_animals = test_animals.filter(func(a): return is_instance_valid(a))
	
	# 更新统计显示
	colonist_stats.text = "殖民者: %d" % test_colonists.size()
	building_stats.text = "建筑: %d" % test_buildings.size()
	
	# 检查系统状态
	var system_status = "正常"
	if test_game_manager:
		if test_colonists.size() > 10:
			system_status = "高负载"
		elif test_colonists.size() == 0 and test_buildings.size() == 0:
			system_status = "空闲"
	else:
		system_status = "错误"
	
	system_stats.text = "系统状态: " + system_status

func spawn_colonist():
	"""生成殖民者"""
	var colonist
	
	# 随机选择殖民者类型
	if randf() > 0.5:
		colonist = full_colonist_scene.new()
		colonist.colonist_name = "测试殖民者 #" + str(test_colonists.size() + 1)
		colonist.age = randi_range(18, 65)
		colonist.gender = ["male", "female"][randi() % 2]
	else:
		colonist = colonist_scene.instantiate()
		colonist.colonist_name = "简单殖民者 #" + str(test_colonists.size() + 1)
	
	# 随机位置
	var spawn_pos = Vector2(
		randf_range(-300, 300),
		randf_range(-300, 300)
	)
	colonist.global_position = spawn_pos
	
	# 添加到场景
	colonists_node.add_child(colonist)
	
	# 注册到GameManager
	test_game_manager.add_colonist(colonist)
	test_colonists.append(colonist)
	
	log_message("殖民者已生成: " + colonist.colonist_name, "green")
	return colonist

func place_building():
	"""放置建筑"""
	var mouse_pos = get_global_mouse_position()
	var grid_pos = Building.world_to_grid(mouse_pos)

	# 检查位置是否可用
	if not test_game_manager.can_place_building_at(grid_pos):
		log_message("无法在此位置放置建筑 (已被占用)", "red")
		return null

	# 创建建筑
	var building = building_scene.new()
	building.building_name = "测试建筑 #" + str(test_buildings.size() + 1)
	building.building_type = ["wall", "door", "bed", "table"][randi() % 4]
	building.set_grid_position(grid_pos)

	# 添加到场景
	buildings_node.add_child(building)

	# 注册到GameManager
	if test_game_manager.add_building(building):
		test_buildings.append(building)
		log_message("建筑已放置: " + building.building_name, "green")

		# 更新网格显示
		if grid_overlay.has_method("queue_redraw"):
			grid_overlay.queue_redraw()

		return building
	else:
		building.queue_free()
		log_message("建筑放置失败", "red")
		return null

func spawn_animal():
	"""生成动物"""
	var animal = Animal.new()
	animal.animal_name = "测试动物 #" + str(test_animals.size() + 1)
	animal.species = ["rabbit", "deer", "bear", "wolf"][randi() % 4]

	# 随机位置
	var spawn_pos = Vector2(
		randf_range(-400, 400),
		randf_range(-400, 400)
	)
	animal.global_position = spawn_pos

	# 添加到场景
	animals_node.add_child(animal)

	# 注册到GameManager
	test_game_manager.add_animal(animal)
	test_animals.append(animal)

	log_message("动物已生成: " + animal.animal_name + " (" + animal.species + ")", "green")
	return animal

func select_entity_at_mouse():
	"""选择鼠标位置的实体"""
	var mouse_pos = get_global_mouse_position()
	var closest_entity = null
	var closest_distance = 50.0

	# 检查殖民者
	for colonist in test_colonists:
		if is_instance_valid(colonist):
			var distance = colonist.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_entity = colonist

	# 检查建筑
	for building in test_buildings:
		if is_instance_valid(building):
			var distance = building.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_entity = building

	# 检查动物
	for animal in test_animals:
		if is_instance_valid(animal):
			var distance = animal.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_entity = animal

	if closest_entity:
		selected_entity = closest_entity
		var entity_name = "未知实体"
		if "colonist_name" in closest_entity:
			entity_name = closest_entity.colonist_name
		elif "building_name" in closest_entity:
			entity_name = closest_entity.building_name
		elif "animal_name" in closest_entity:
			entity_name = closest_entity.animal_name

		log_message("选择了: " + entity_name, "yellow")
	else:
		selected_entity = null
		log_message("未选择任何实体", "gray")

func remove_entity_at_mouse():
	"""移除鼠标位置的实体"""
	var mouse_pos = get_global_mouse_position()

	# 检查建筑
	var building = test_game_manager.get_building_at_world_pos(mouse_pos)
	if building:
		test_game_manager.remove_building(building)
		test_buildings.erase(building)
		building.queue_free()
		log_message("建筑已移除", "orange")

		# 更新网格显示
		if grid_overlay.has_method("queue_redraw"):
			grid_overlay.queue_redraw()
		return

	# 检查其他实体
	var closest_entity = null
	var closest_distance = 30.0

	for colonist in test_colonists:
		if is_instance_valid(colonist):
			var distance = colonist.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_entity = colonist

	for animal in test_animals:
		if is_instance_valid(animal):
			var distance = animal.global_position.distance_to(mouse_pos)
			if distance < closest_distance:
				closest_distance = distance
				closest_entity = animal

	if closest_entity:
		if closest_entity in test_colonists:
			test_game_manager.remove_colonist(closest_entity)
			test_colonists.erase(closest_entity)
			log_message("殖民者已移除", "orange")
		elif closest_entity in test_animals:
			test_game_manager.remove_animal(closest_entity)
			test_animals.erase(closest_entity)
			log_message("动物已移除", "orange")

		closest_entity.queue_free()
	else:
		log_message("鼠标位置没有可移除的实体", "gray")

func toggle_grid_display():
	"""切换网格显示"""
	if grid_overlay.has_method("toggle_grid_display"):
		grid_overlay.toggle_grid_display()
		log_message("网格显示: " + str(grid_overlay.show_grid), "blue")

func toggle_ui_display():
	"""切换UI显示"""
	ui_visible = not ui_visible
	ui.visible = ui_visible
	log_message("UI显示: " + str(ui_visible), "blue")

# 测试场景方法
func simulate_raid():
	"""模拟袭击"""
	log_message("模拟袭击开始...", "red")

	# 生成敌对殖民者
	for i in range(3):
		var raider = full_colonist_scene.new()
		raider.colonist_name = "袭击者 #" + str(i + 1)
		raider.faction = "hostile"

		# 在地图边缘生成
		var spawn_pos = Vector2(
			randf_range(-500, 500),
			randf_range(-500, 500)
		)
		raider.global_position = spawn_pos

		colonists_node.add_child(raider)
		test_colonists.append(raider)

		log_message("袭击者已生成: " + raider.colonist_name, "red")

	log_message("袭击模拟完成", "red")

func simulate_trade():
	"""模拟贸易"""
	log_message("贸易商队到达...", "green")

	# 生成友好商人
	var trader = full_colonist_scene.new()
	trader.colonist_name = "贸易商"
	trader.faction = "trader"

	var spawn_pos = Vector2(0, -200)
	trader.global_position = spawn_pos

	colonists_node.add_child(trader)
	test_colonists.append(trader)

	log_message("贸易商已到达: " + trader.colonist_name, "green")

func trigger_random_event():
	"""触发随机事件"""
	var events = [
		"太阳耀斑",
		"暴雨",
		"极光",
		"陨石雨",
		"动物迁徙"
	]

	var event_name = events[randi() % events.size()]
	log_message("事件触发: " + event_name, "purple")

	# 根据事件类型执行不同效果
	match event_name:
		"太阳耀斑":
			log_message("电力系统受到影响", "orange")
		"暴雨":
			log_message("移动速度降低", "blue")
		"极光":
			log_message("殖民者心情提升", "cyan")
		"陨石雨":
			log_message("建筑可能受损", "red")
		"动物迁徙":
			# 生成一些动物
			for i in range(5):
				spawn_animal()
			log_message("大量动物出现", "green")

# UI按钮回调
func _on_spawn_colonist_pressed():
	spawn_colonist()

func _on_place_building_pressed():
	place_building()

func _on_spawn_animal_pressed():
	spawn_animal()

func _on_test_raid_pressed():
	simulate_raid()

func _on_test_trade_pressed():
	simulate_trade()

func _on_test_event_pressed():
	trigger_random_event()

func _on_toggle_grid_pressed():
	toggle_grid_display()

func _on_toggle_ui_pressed():
	toggle_ui_display()

func _on_exit_test_pressed():
	exit_test()

func exit_test():
	"""退出测试"""
	log_message("=== 全系统综合测试结束 ===", "cyan")
	log_message("测试统计:", "cyan")
	log_message("- 殖民者数量: " + str(test_colonists.size()), "white")
	log_message("- 建筑数量: " + str(test_buildings.size()), "white")
	log_message("- 动物数量: " + str(test_animals.size()), "white")

	# 等待2秒后返回主场景
	await get_tree().create_timer(2.0).timeout
	get_tree().change_scene_to_file("res://scenes/Main.tscn")
