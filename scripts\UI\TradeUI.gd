extends Control
class_name TradeUI

# 贸易界面 - 与商人交易

signal trade_executed(trader, buy_items: Array, sell_items: Array)

@onready var trader_info: Label = $VBoxContainer/TraderInfo
@onready var trader_goods: ItemList = $HBoxContainer/TraderGoods
@onready var player_goods: ItemList = $HBoxContainer/PlayerGoods
@onready var trade_summary: RichTextLabel = $VBoxContainer/TradeSummary
@onready var execute_trade_button: Button = $VBoxContainer/ExecuteTradeButton

var trade_system: TradeSystem
var current_trader = null
var selected_buy_items: Array = []
var selected_sell_items: Array = []

func _ready():
	setup_ui()
	if GameManager.instance and GameManager.instance.trade_system:
		trade_system = GameManager.instance.trade_system
		trade_system.trader_arrived.connect(_on_trader_arrived)
		trade_system.trader_departed.connect(_on_trader_departed)

func setup_ui():
	trader_goods.item_selected.connect(_on_trader_item_selected)
	player_goods.item_selected.connect(_on_player_item_selected)
	execute_trade_button.pressed.connect(_on_execute_trade)
	
	# 初始状态
	visible = false
	execute_trade_button.disabled = true

func _on_trader_arrived(trader):
	current_trader = trader
	show_trader_interface()

func _on_trader_departed(trader):
	if current_trader == trader:
		hide_trader_interface()

func show_trader_interface():
	if not current_trader:
		return
	
	visible = true
	update_trader_info()
	populate_trader_goods()
	populate_player_goods()
	update_trade_summary()

func hide_trader_interface():
	visible = false
	current_trader = null
	selected_buy_items.clear()
	selected_sell_items.clear()

func update_trader_info():
	if not current_trader:
		return
	
	var info = trade_system.get_trader_info(current_trader)
	trader_info.text = "Trader: " + info["name"] + " (" + info["faction"] + ")\n"
	trader_info.text += "Silver: " + str(info["silver"]) + "\n"
	trader_info.text += "Time Remaining: " + str(int(info["time_remaining"])) + "s"

func populate_trader_goods():
	trader_goods.clear()
	if not current_trader:
		return
	
	var available_items = trade_system.get_available_items_for_purchase(current_trader)
	for item in available_items:
		var price = trade_system.calculate_item_price(item, true)
		var item_text = item.name + " x" + str(item.quantity)
		item_text += " (" + item.quality + ") - " + str(price) + " silver"
		trader_goods.add_item(item_text)
		trader_goods.set_item_metadata(trader_goods.get_item_count() - 1, item)

func populate_player_goods():
	player_goods.clear()
	if not trade_system:
		return
	
	var sellable_items = trade_system.get_available_items_for_sale()
	for item in sellable_items:
		var price = trade_system.calculate_item_price(item, false)
		var item_text = item.name + " x" + str(item.quantity)
		item_text += " - " + str(price) + " silver"
		player_goods.add_item(item_text)
		player_goods.set_item_metadata(player_goods.get_item_count() - 1, item)

func _on_trader_item_selected(index: int):
	var item = trader_goods.get_item_metadata(index)
	if item:
		toggle_buy_item(item)
		update_trade_summary()

func _on_player_item_selected(index: int):
	var item = player_goods.get_item_metadata(index)
	if item:
		toggle_sell_item(item)
		update_trade_summary()

func toggle_buy_item(item):
	if item in selected_buy_items:
		selected_buy_items.erase(item)
	else:
		selected_buy_items.append(item)

func toggle_sell_item(item):
	if item in selected_sell_items:
		selected_sell_items.erase(item)
	else:
		selected_sell_items.append(item)

func update_trade_summary():
	var total_cost = 0
	var total_income = 0
	
	var summary_text = "[b]Trade Summary[/b]\n\n"
	
	if selected_buy_items.size() > 0:
		summary_text += "[b]Buying:[/b]\n"
		for item in selected_buy_items:
			var price = trade_system.calculate_item_price(item, true)
			total_cost += price * item.quantity
			summary_text += "• " + item.name + " x" + str(item.quantity) + " - " + str(price * item.quantity) + " silver\n"
		summary_text += "\n"
	
	if selected_sell_items.size() > 0:
		summary_text += "[b]Selling:[/b]\n"
		for item in selected_sell_items:
			var price = trade_system.calculate_item_price(item, false)
			total_income += price * item.quantity
			summary_text += "• " + item.name + " x" + str(item.quantity) + " - " + str(price * item.quantity) + " silver\n"
		summary_text += "\n"
	
	var net_cost = total_cost - total_income
	summary_text += "[b]Net Cost:[/b] " + str(net_cost) + " silver\n"
	
	var player_silver = GameManager.instance.get_resource("silver")
	summary_text += "[b]Your Silver:[/b] " + str(player_silver) + "\n"
	
	if net_cost > player_silver:
		summary_text += "[color=red]Insufficient silver![/color]"
		execute_trade_button.disabled = true
	elif selected_buy_items.size() == 0 and selected_sell_items.size() == 0:
		execute_trade_button.disabled = true
	else:
		execute_trade_button.disabled = false
	
	trade_summary.text = summary_text

func _on_execute_trade():
	if not current_trader or not trade_system:
		return
	
	if trade_system.attempt_trade(current_trader, selected_buy_items, selected_sell_items):
		trade_executed.emit(current_trader, selected_buy_items, selected_sell_items)
		
		# 清除选择并刷新界面
		selected_buy_items.clear()
		selected_sell_items.clear()
		populate_trader_goods()
		populate_player_goods()
		update_trader_info()
		update_trade_summary()
		
		show_trade_success_notification()
	else:
		show_trade_failed_notification()

func show_trade_success_notification():
	var notification_dialog = AcceptDialog.new()
	notification_dialog.dialog_text = "Trade completed successfully!"
	notification_dialog.title = "Trade Success"
	add_child(notification_dialog)
	notification_dialog.popup_centered()

	var timer = Timer.new()
	timer.wait_time = 2.0
	timer.timeout.connect(func(): notification_dialog.queue_free())
	timer.autostart = true
	notification_dialog.add_child(timer)

func show_trade_failed_notification():
	var notification_dialog = AcceptDialog.new()
	notification_dialog.dialog_text = "Trade failed! Check your silver and trader's capacity."
	notification_dialog.title = "Trade Failed"
	add_child(notification_dialog)
	notification_dialog.popup_centered()

	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.timeout.connect(func(): notification_dialog.queue_free())
	timer.autostart = true
	notification_dialog.add_child(timer)

func force_trader_arrival():
	if trade_system:
		trade_system.spawn_trader()

func _process(_delta):
	# 更新商人信息（时间倒计时）
	if visible and current_trader:
		update_trader_info()
