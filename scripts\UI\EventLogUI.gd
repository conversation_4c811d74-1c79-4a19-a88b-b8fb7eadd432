extends Control
class_name EventLogUI

# 事件日志界面 - 显示游戏事件和通知

@onready var event_log: RichTextLabel = $VBoxContainer/EventLog
@onready var clear_log_button: Button = $VBoxContainer/ClearButton
@onready var auto_scroll_check: CheckBox = $VBoxContainer/AutoScrollCheck

var event_system: EventSystem
var max_log_entries: int = 100
var auto_scroll: bool = true

func _ready():
	setup_ui()
	if GameManager.instance and GameManager.instance.event_system:
		event_system = GameManager.instance.event_system
		connect_event_signals()

func setup_ui():
	clear_log_button.pressed.connect(_on_clear_log)
	auto_scroll_check.toggled.connect(_on_auto_scroll_toggled)
	auto_scroll_check.button_pressed = auto_scroll
	
	# 初始化日志
	event_log.bbcode_enabled = true
	add_log_entry("Game started", "SYSTEM")

func connect_event_signals():
	if not event_system:
		return
	
	event_system.event_triggered.connect(_on_event_triggered)
	event_system.trader_arrived.connect(_on_trader_arrived)
	event_system.weather_changed.connect(_on_weather_changed)
	event_system.raid_started.connect(_on_raid_started)

func _on_event_triggered(event_data: Dictionary):
	var _event_type = EventSystem.EventType.keys()[event_data["type"]]
	var message = format_event_message(event_data)
	add_log_entry(message, "EVENT")

func _on_trader_arrived(trader_data: Dictionary):
	var message = "Trader " + trader_data["trader_name"] + " has arrived with goods!"
	add_log_entry(message, "TRADE")

func _on_weather_changed(weather_type: String):
	var message = "Weather changed to: " + weather_type.replace("_", " ").capitalize()
	add_log_entry(message, "WEATHER")

func _on_raid_started(raid_data: Dictionary):
	var message = "RAID ALERT! " + str(raid_data["raider_count"]) + " " + raid_data["raider_type"] + " approaching!"
	add_log_entry(message, "DANGER")

func format_event_message(event_data: Dictionary) -> String:
	var event_type = event_data["type"]
	
	match event_type:
		EventSystem.EventType.RESOURCE_DROP:
			return "Resource drop: +" + str(event_data["amount"]) + " " + event_data["resource_type"]
		
		EventSystem.EventType.ANIMAL_MIGRATION:
			return str(event_data["count"]) + " " + event_data["animal_type"] + " are migrating through the area"
		
		EventSystem.EventType.DISEASE_OUTBREAK:
			return "Disease outbreak: " + event_data["disease_name"] + " is spreading!"
		
		EventSystem.EventType.SOLAR_FLARE:
			return "Solar flare! All electrical devices disabled for " + str(int(event_data["duration"])) + " seconds"
		
		EventSystem.EventType.METEOR_SHOWER:
			return "Meteor shower! " + str(event_data["meteor_count"]) + " meteors incoming!"
		
		EventSystem.EventType.REFUGEE_ARRIVAL:
			return "Refugee " + event_data["refugee_name"] + " seeks shelter"
		
		EventSystem.EventType.ANCIENT_DANGER:
			return "Ancient danger awakened: " + event_data["danger_type"]
		
		_:
			return "Unknown event occurred"

func add_log_entry(message: String, category: String = "INFO"):
	var timestamp = get_formatted_time()
	var color = get_category_color(category)
	var category_tag = "[" + category + "]"
	
	var log_entry = "[color=" + color + "]" + timestamp + " " + category_tag + " " + message + "[/color]\n"
	
	event_log.append_text(log_entry)
	
	# 限制日志条目数量
	limit_log_entries()
	
	# 自动滚动到底部
	if auto_scroll:
		scroll_to_bottom()

func get_formatted_time() -> String:
	if GameManager.instance:
		var total_seconds = int(GameManager.instance.game_time)
		var hours = floori(float(total_seconds) / 3600.0) % 24  # 使用floori明确整数除法
		var minutes = floori(float(total_seconds) / 60.0) % 60  # 使用floori明确整数除法
		return "%02d:%02d" % [hours, minutes]
	return "00:00"

func get_category_color(category: String) -> String:
	match category:
		"SYSTEM": return "cyan"
		"EVENT": return "yellow"
		"TRADE": return "green"
		"WEATHER": return "lightblue"
		"DANGER": return "red"
		"RESEARCH": return "purple"
		"CONSTRUCTION": return "orange"
		"COLONIST": return "white"
		_: return "gray"

func limit_log_entries():
	var text = event_log.text
	var lines = text.split("\n")
	
	if lines.size() > max_log_entries:
		var excess = lines.size() - max_log_entries
		for i in range(excess):
			lines.remove_at(0)
		
		event_log.text = "\n".join(lines)

func scroll_to_bottom():
	# 等待一帧确保文本已更新
	await get_tree().process_frame
	var scroll_container = event_log.get_parent()
	if scroll_container is ScrollContainer:
		scroll_container.scroll_vertical = scroll_container.get_v_scroll_bar().max_value

func _on_clear_log():
	event_log.clear()
	add_log_entry("Log cleared", "SYSTEM")

func _on_auto_scroll_toggled(pressed: bool):
	auto_scroll = pressed

# 添加特定类型的日志条目
func add_colonist_log(colonist_name: String, message: String):
	add_log_entry(colonist_name + ": " + message, "COLONIST")

func add_research_log(message: String):
	add_log_entry(message, "RESEARCH")

func add_construction_log(message: String):
	add_log_entry(message, "CONSTRUCTION")

func add_trade_log(message: String):
	add_log_entry(message, "TRADE")

func add_danger_log(message: String):
	add_log_entry(message, "DANGER")

# 连接到游戏管理器的信号
func connect_game_signals():
	if GameManager.instance:
		GameManager.instance.colonist_added.connect(_on_colonist_added)
		GameManager.instance.building_built.connect(_on_building_built)
		GameManager.instance.resource_changed.connect(_on_resource_changed)

func _on_colonist_added(colonist):
	add_colonist_log(colonist.colonist_name, "joined the colony")

func _on_building_built(building):
	add_construction_log(building.building_name + " construction completed")

func _on_resource_changed(resource_type: String, amount: int):
	# 只记录重要的资源变化
	if amount > 50 or amount < -20:
		var change_text = "+" + str(amount) if amount > 0 else str(amount)
		add_log_entry("Resource change: " + change_text + " " + resource_type, "INFO")

# 导出日志功能
func export_log_to_file():
	var file = FileAccess.open("user://game_log.txt", FileAccess.WRITE)
	if file:
		file.store_string(event_log.text)
		file.close()
		add_log_entry("Log exported to game_log.txt", "SYSTEM")
	else:
		add_log_entry("Failed to export log", "SYSTEM")

# 搜索功能
func search_log(search_term: String):
	var text = event_log.text
	var lines = text.split("\n")
	var results = []
	
	for i in range(lines.size()):
		if search_term.to_lower() in lines[i].to_lower():
			results.append({"line": i, "text": lines[i]})
	
	if results.size() > 0:
		add_log_entry("Found " + str(results.size()) + " results for '" + search_term + "'", "SYSTEM")
	else:
		add_log_entry("No results found for '" + search_term + "'", "SYSTEM")
	
	return results
