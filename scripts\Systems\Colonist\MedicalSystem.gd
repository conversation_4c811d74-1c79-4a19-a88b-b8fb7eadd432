class_name MedicalSystem
extends Node

# 医疗系统
# 管理殖民者的健康、疾病、治疗等

signal colonist_injured(colonist: Node, injury_type: String)
signal colonist_healed(colonist: Node, heal_amount: float)
@warning_ignore("unused_signal")
signal disease_outbreak(disease_name: String, affected_colonists: Array) # 疾病爆发信号，供UI和其他系统监听

var active_injuries: Dictionary = {}
var active_diseases: Dictionary = {}
var medical_supplies: Dictionary = {
	"medicine": 0,
	"bandages": 0,
	"antibiotics": 0
}

# 伤病类型定义
enum InjuryType {
	MINOR_CUT,
	DEEP_WOUND,
	BROKEN_BONE,
	BURN,
	INFECTION
}

enum DiseaseType {
	COMMON_COLD,
	FOOD_POISONING,
	PLAGUE,
	MALARIA
}

func _ready():
	name = "MedicalSystem"

func register_colonist(colonist: Node):
	"""注册殖民者到医疗系统"""
	var colonist_id = str(colonist.get_instance_id())

	# 初始化殖民者的医疗记录
	if not active_injuries.has(colonist_id):
		active_injuries[colonist_id] = []
	if not active_diseases.has(colonist_id):
		active_diseases[colonist_id] = []

	print("Registered colonist for medical system: ", colonist.colonist_name if "colonist_name" in colonist else "Unknown")

func unregister_colonist(colonist: Node):
	"""从医疗系统注销殖民者"""
	var colonist_id = str(colonist.get_instance_id())

	active_injuries.erase(colonist_id)
	active_diseases.erase(colonist_id)

	print("Unregistered colonist from medical system: ", colonist.colonist_name if "colonist_name" in colonist else "Unknown")

func _process(delta: float):
	update_injuries(delta)
	update_diseases(delta)
	check_for_disease_spread(delta)

func update_injuries(delta: float):
	for colonist_id in active_injuries.keys():
		var injuries = active_injuries[colonist_id]
		for i in range(injuries.size() - 1, -1, -1):
			var injury = injuries[i]
			injury.healing_progress += delta * injury.healing_rate
			
			if injury.healing_progress >= 1.0:
				heal_injury(colonist_id, i)

func update_diseases(delta: float):
	for colonist_id in active_diseases.keys():
		var diseases = active_diseases[colonist_id]
		for i in range(diseases.size() - 1, -1, -1):
			var disease = diseases[i]
			disease.duration -= delta
			
			if disease.duration <= 0:
				cure_disease(colonist_id, i)

func add_injury(colonist: Node, injury_type: InjuryType, severity: float = 1.0):
	var colonist_id = str(colonist.get_instance_id())
	
	if not active_injuries.has(colonist_id):
		active_injuries[colonist_id] = []
	
	var injury_data = create_injury_data(injury_type, severity)
	active_injuries[colonist_id].append(injury_data)
	
	# 应用伤害效果
	apply_injury_effects(colonist, injury_data)
	
	colonist_injured.emit(colonist, get_injury_name(injury_type))

func create_injury_data(injury_type: InjuryType, severity: float) -> Dictionary:
	var base_data = get_injury_base_data(injury_type)
	return {
		"type": injury_type,
		"severity": severity,
		"healing_progress": 0.0,
		"healing_rate": base_data.healing_rate * (1.0 / severity),
		"pain_level": base_data.pain_level * severity,
		"mobility_penalty": base_data.mobility_penalty * severity,
		"work_penalty": base_data.work_penalty * severity
	}

func get_injury_base_data(injury_type: InjuryType) -> Dictionary:
	match injury_type:
		InjuryType.MINOR_CUT:
			return {
				"healing_rate": 0.1,
				"pain_level": 2,
				"mobility_penalty": 0.0,
				"work_penalty": 0.05
			}
		InjuryType.DEEP_WOUND:
			return {
				"healing_rate": 0.05,
				"pain_level": 8,
				"mobility_penalty": 0.1,
				"work_penalty": 0.2
			}
		InjuryType.BROKEN_BONE:
			return {
				"healing_rate": 0.02,
				"pain_level": 15,
				"mobility_penalty": 0.3,
				"work_penalty": 0.4
			}
		InjuryType.BURN:
			return {
				"healing_rate": 0.03,
				"pain_level": 12,
				"mobility_penalty": 0.05,
				"work_penalty": 0.25
			}
		InjuryType.INFECTION:
			return {
				"healing_rate": 0.01,
				"pain_level": 10,
				"mobility_penalty": 0.15,
				"work_penalty": 0.3
			}
		_:
			return {
				"healing_rate": 0.1,
				"pain_level": 1,
				"mobility_penalty": 0.0,
				"work_penalty": 0.0
			}

func apply_injury_effects(colonist: Node, injury_data: Dictionary):
	if colonist.has_method("apply_injury_penalty"):
		colonist.apply_injury_penalty(injury_data)

func heal_injury(colonist_id: String, injury_index: int):
	if active_injuries.has(colonist_id) and injury_index < active_injuries[colonist_id].size():
		var injury = active_injuries[colonist_id][injury_index]
		active_injuries[colonist_id].remove_at(injury_index)
		
		if active_injuries[colonist_id].is_empty():
			active_injuries.erase(colonist_id)
		
		# 通知殖民者伤势愈合
		var colonist = instance_from_id(int(colonist_id))
		if colonist and colonist.has_method("remove_injury_penalty"):
			colonist.remove_injury_penalty(injury)
		
		colonist_healed.emit(colonist, injury.severity * 10)

func add_disease(colonist: Node, disease_type: DiseaseType, severity: float = 1.0):
	var colonist_id = str(colonist.get_instance_id())
	
	if not active_diseases.has(colonist_id):
		active_diseases[colonist_id] = []
	
	var disease_data = create_disease_data(disease_type, severity)
	active_diseases[colonist_id].append(disease_data)
	
	apply_disease_effects(colonist, disease_data)

func create_disease_data(disease_type: DiseaseType, severity: float) -> Dictionary:
	var base_data = get_disease_base_data(disease_type)
	return {
		"type": disease_type,
		"severity": severity,
		"duration": base_data.duration * severity,
		"contagious": base_data.contagious,
		"symptoms": base_data.symptoms,
		"health_drain": base_data.health_drain * severity
	}

func get_disease_base_data(disease_type: DiseaseType) -> Dictionary:
	match disease_type:
		DiseaseType.COMMON_COLD:
			return {
				"duration": 168.0,  # 7 days
				"contagious": true,
				"symptoms": ["cough", "fatigue"],
				"health_drain": 0.1
			}
		DiseaseType.FOOD_POISONING:
			return {
				"duration": 48.0,   # 2 days
				"contagious": false,
				"symptoms": ["nausea", "weakness"],
				"health_drain": 0.3
			}
		DiseaseType.PLAGUE:
			return {
				"duration": 336.0,  # 14 days
				"contagious": true,
				"symptoms": ["fever", "weakness", "pain"],
				"health_drain": 0.5
			}
		DiseaseType.MALARIA:
			return {
				"duration": 240.0,  # 10 days
				"contagious": false,
				"symptoms": ["fever", "chills", "fatigue"],
				"health_drain": 0.4
			}
		_:
			return {
				"duration": 24.0,
				"contagious": false,
				"symptoms": [],
				"health_drain": 0.1
			}

func apply_disease_effects(colonist: Node, disease_data: Dictionary):
	if colonist.has_method("apply_disease_penalty"):
		colonist.apply_disease_penalty(disease_data)

func cure_disease(colonist_id: String, disease_index: int):
	if active_diseases.has(colonist_id) and disease_index < active_diseases[colonist_id].size():
		var disease = active_diseases[colonist_id][disease_index]
		active_diseases[colonist_id].remove_at(disease_index)
		
		if active_diseases[colonist_id].is_empty():
			active_diseases.erase(colonist_id)
		
		var colonist = instance_from_id(int(colonist_id))
		if colonist and colonist.has_method("remove_disease_penalty"):
			colonist.remove_disease_penalty(disease)

func check_for_disease_spread(_delta: float):
	# 检查传染病传播
	for colonist_id in active_diseases.keys():
		var diseases = active_diseases[colonist_id]
		for disease in diseases:
			if disease.contagious and randf() < 0.001:  # 0.1% chance per frame
				spread_disease(disease.type, disease.severity * 0.8)

func spread_disease(disease_type: DiseaseType, severity: float):
	# 简化的疾病传播逻辑
	var all_colonists = get_tree().get_nodes_in_group("colonists")
	if all_colonists.size() > 0:
		var target = all_colonists[randi() % all_colonists.size()]
		add_disease(target, disease_type, severity)

func get_injury_name(injury_type: InjuryType) -> String:
	match injury_type:
		InjuryType.MINOR_CUT: return "Minor Cut"
		InjuryType.DEEP_WOUND: return "Deep Wound"
		InjuryType.BROKEN_BONE: return "Broken Bone"
		InjuryType.BURN: return "Burn"
		InjuryType.INFECTION: return "Infection"
		_: return "Unknown Injury"

func get_disease_name(disease_type: DiseaseType) -> String:
	match disease_type:
		DiseaseType.COMMON_COLD: return "Common Cold"
		DiseaseType.FOOD_POISONING: return "Food Poisoning"
		DiseaseType.PLAGUE: return "Plague"
		DiseaseType.MALARIA: return "Malaria"
		_: return "Unknown Disease"

func get_colonist_health_status(colonist: Node) -> Dictionary:
	var colonist_id = str(colonist.get_instance_id())
	return {
		"injuries": active_injuries.get(colonist_id, []),
		"diseases": active_diseases.get(colonist_id, []),
		"overall_health": calculate_overall_health(colonist_id)
	}

func calculate_overall_health(colonist_id: String) -> float:
	var health_penalty = 0.0
	
	if active_injuries.has(colonist_id):
		for injury in active_injuries[colonist_id]:
			health_penalty += injury.pain_level * injury.severity
	
	if active_diseases.has(colonist_id):
		for disease in active_diseases[colonist_id]:
			health_penalty += disease.health_drain * 10
	
	return max(0.0, 100.0 - health_penalty)
