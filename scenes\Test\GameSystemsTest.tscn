[gd_scene load_steps=2 format=3 uid="uid://bbva2ivi5whdr"]

[ext_resource type="Script" uid="uid://ct80xc1isnv6j" path="res://scripts/Test/GameSystemsTestController.gd" id="1_2k3l4"]

[node name="GameSystemsTest" type="Node2D"]
script = ExtResource("1_2k3l4")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]

[node name="MainPanel" type="Panel" parent="UI"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="HBoxContainer" type="HBoxContainer" parent="UI/MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftPanel" type="VBoxContainer" parent="UI/MainPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Title" type="Label" parent="UI/MainPanel/HBoxContainer/LeftPanel"]
layout_mode = 2
text = "游戏系统测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/MainPanel/HBoxContainer/LeftPanel"]
layout_mode = 2

[node name="SystemTests" type="VBoxContainer" parent="UI/MainPanel/HBoxContainer/LeftPanel"]
layout_mode = 2

[node name="TestMedical" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试医疗系统"

[node name="TestCombat" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试战斗系统"

[node name="TestWork" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试工作系统"

[node name="TestMood" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试心情系统"

[node name="TestResearch" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试研究系统"

[node name="TestTrade" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试贸易系统"

[node name="TestEvent" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试事件系统"

[node name="TestFaction" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"]
layout_mode = 2
text = "测试派系系统"

[node name="HSeparator2" type="HSeparator" parent="UI/MainPanel/HBoxContainer/LeftPanel"]
layout_mode = 2

[node name="ControlButtons" type="VBoxContainer" parent="UI/MainPanel/HBoxContainer/LeftPanel"]
layout_mode = 2

[node name="RunAllTests" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons"]
layout_mode = 2
text = "运行所有测试"

[node name="ClearLog" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons"]
layout_mode = 2
text = "清除日志"

[node name="ExitTest" type="Button" parent="UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons"]
layout_mode = 2
text = "退出测试"

[node name="RightPanel" type="VBoxContainer" parent="UI/MainPanel/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="LogTitle" type="Label" parent="UI/MainPanel/HBoxContainer/RightPanel"]
layout_mode = 2
text = "测试日志"
horizontal_alignment = 1

[node name="HSeparator3" type="HSeparator" parent="UI/MainPanel/HBoxContainer/RightPanel"]
layout_mode = 2

[node name="LogContainer" type="ScrollContainer" parent="UI/MainPanel/HBoxContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="LogText" type="RichTextLabel" parent="UI/MainPanel/HBoxContainer/RightPanel/LogContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
bbcode_enabled = true
text = "[color=green]游戏系统测试准备就绪[/color]"
