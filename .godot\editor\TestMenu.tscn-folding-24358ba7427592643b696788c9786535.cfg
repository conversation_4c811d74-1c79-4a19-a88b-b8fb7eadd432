[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("CenterContainer"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/Title"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/Subtitle"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/SingleSystemTests"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/HSeparator"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/GridSystemTest"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/ColonistSystemTest"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/GameSystemsTest"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/HSeparator2"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/ComprehensiveTests"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/HSeparator3"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/AllSystemsTest"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/TestButtons/HSeparator4"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/ControlButtons"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/ControlButtons/BackToMain"), PackedStringArray("Layout"), NodePath("CenterContainer/MainPanel/VBoxContainer/ControlButtons/ExitGame"), PackedStringArray("Layout"), NodePath("InfoPanel"), PackedStringArray("Layout"), NodePath("InfoPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("InfoPanel/VBoxContainer/InfoTitle"), PackedStringArray("Layout"), NodePath("InfoPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("InfoPanel/VBoxContainer/InfoText"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
