extends CanvasLayer
class_name GameUI

# 游戏 UI 管理器 - 简化版本

# 游戏状态
var game_paused: bool = false
var current_time_speed: float = 1.0

# 选中的对象
var selected_colonist: Colonist = null

# 建筑模式
var is_building_mode: bool = false
var selected_building_type: int = 0

func _ready():
	print("GameUI initialized")
	connect_signals()
	update_resource_display()

func connect_signals():
	# 连接游戏管理器信号
	if GameManager:
		if GameManager.colonist_added.is_connected(_on_colonist_added):
			GameManager.colonist_added.disconnect(_on_colonist_added)
		GameManager.colonist_added.connect(_on_colonist_added)
		if GameManager.building_built.is_connected(_on_building_built):
			GameManager.building_built.disconnect(_on_building_built)
		GameManager.building_built.connect(_on_building_built)

func update_resource_display():
	if not GameManager:
		return
	
	var resources = GameManager.resources
	print("Resources - Food: ", resources.get("food", 0), " Wood: ", resources.get("wood", 0))

func _on_colonist_added(colonist: Colonist):
	print("UI: New colonist added - ", colonist.colonist_name)

func _on_building_built(building: Building):
	print("UI: New building built - ", building.building_name)

# 更新函数（由主循环调用）
func _process(_delta):
	if GameManager:
		# 每秒更新一次资源显示
		if Engine.get_process_frames() % 60 == 0:
			update_resource_display()

# 清理函数
func _exit_tree():
	# 断开所有信号连接
	if GameManager:
		if GameManager.colonist_added.is_connected(_on_colonist_added):
			GameManager.colonist_added.disconnect(_on_colonist_added)
		if GameManager.building_built.is_connected(_on_building_built):
			GameManager.building_built.disconnect(_on_building_built)
