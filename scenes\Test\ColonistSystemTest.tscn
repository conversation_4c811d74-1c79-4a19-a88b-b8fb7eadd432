[gd_scene load_steps=2 format=3 uid="uid://bl371a8ih8qtk"]

[ext_resource type="Script" uid="uid://c81jkk520amxd" path="res://scripts/Test/ColonistSystemTestController.gd" id="1_1j2k3"]

[node name="ColonistSystemTest" type="Node2D"]
script = ExtResource("1_1j2k3")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(1.5, 1.5)

[node name="UI" type="CanvasLayer" parent="."]

[node name="TestPanel" type="Panel" parent="UI"]
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -400.0
offset_top = -300.0
offset_right = -10.0
offset_bottom = -10.0

[node name="VBoxContainer" type="VBoxContainer" parent="UI/TestPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "殖民者系统测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2

[node name="SpawnColonist" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "生成殖民者"

[node name="SpawnMultiple" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "生成5个殖民者"

[node name="TestHealth" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "测试健康系统"

[node name="TestMood" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "测试心情系统"

[node name="TestSkills" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "测试技能系统"

[node name="ClearAll" type="Button" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "清除所有殖民者"

[node name="HSeparator2" type="HSeparator" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2

[node name="StatsLabel" type="Label" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "统计信息:
殖民者数量: 0
平均健康: 0%
平均心情: 0%"
vertical_alignment = 1

[node name="HSeparator3" type="HSeparator" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2

[node name="InfoLabel" type="Label" parent="UI/TestPanel/VBoxContainer"]
layout_mode = 2
text = "WASD: 移动相机
鼠标点击: 选择殖民者
ESC: 退出测试"
vertical_alignment = 1

[node name="Colonists" type="Node2D" parent="."]
