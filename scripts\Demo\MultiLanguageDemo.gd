extends Control
class_name MultiLanguageDemo

# 多语言演示界面 - 展示GameConfig数据的多语言显示

@onready var demo_container: VBoxContainer = $VBoxContainer
@onready var language_switch: HBoxContainer = $VBoxContainer/LanguageSwitch
@onready var content_display: RichTextLabel = $VBoxContainer/ContentDisplay

var current_demo_type: String = "faction"

func _ready():
	setup_demo_ui()
	generate_demo_content()

func setup_demo_ui():
	# 创建语言切换按钮
	var english_btn = Button.new()
	english_btn.text = "English"
	english_btn.pressed.connect(func(): switch_language(LocalizationManager.Language.ENGLISH))
	language_switch.add_child(english_btn)
	
	var chinese_btn = Button.new()
	chinese_btn.text = "中文"
	chinese_btn.pressed.connect(func(): switch_language(LocalizationManager.Language.CHINESE))
	language_switch.add_child(chinese_btn)
	
	# 创建内容类型切换按钮
	var content_types = ["faction", "animal", "weapon", "skill", "trait"]
	for content_type in content_types:
		var btn = Button.new()
		btn.text = content_type.capitalize()
		btn.pressed.connect(func(): switch_content_type(content_type))
		language_switch.add_child(btn)

func switch_language(language: LocalizationManager.Language):
	if LocalizationManager:
		LocalizationManager.set_language(language)
		generate_demo_content()

func switch_content_type(content_type: String):
	current_demo_type = content_type
	generate_demo_content()

func generate_demo_content():
	var content = "[center][b]多语言数据展示 / Multilingual Data Display[/b][/center]\n\n"

	# 显示当前语言
	var current_lang = LocalizationManager.get_language_name(LocalizationManager.get_current_language())
	content += "[b]当前语言 / Current Language:[/b] " + current_lang + "\n\n"
	
	# 显示选中的内容类型
	content += "[b]内容类型 / Content Type:[/b] " + current_demo_type.capitalize() + "\n\n"
	
	# 获取并显示数据
	var config_data = GameConfig.get_config(current_demo_type)
	
	match current_demo_type:
		"faction":
			content += generate_faction_demo(config_data)
		"animal":
			content += generate_animal_demo(config_data)
		"weapon":
			content += generate_weapon_demo(config_data)
		"skill":
			content += generate_skill_demo(config_data)
		"trait":
			content += generate_trait_demo(config_data)
	
	content_display.text = content

func generate_faction_demo(config_data: Dictionary) -> String:
	var content = "[b]" + LocalizationManager.tr_ui("factions") + " / Factions:[/b]\n\n"
	
	var faction_keys = ["tribal", "outlander", "pirate", "mechanoid", "spacer", "imperial"]
	for faction_key in faction_keys:
		if faction_key in config_data:
			var faction_config = config_data[faction_key]
			var localized_name = LocalizationManager.tr_data("faction", faction_key)
			
			content += "[b]• " + localized_name + "[/b]\n"
			content += "  " + LocalizationManager.tr_ui("tech_level") + ": " + str(faction_config.get("tech", 0)) + "\n"
			content += "  " + LocalizationManager.tr_ui("population") + ": " + str(faction_config.get("pop", [0, 0])) + "\n"
			content += "  " + LocalizationManager.tr_ui("military") + ": " + str(faction_config.get("military", [0, 0])) + "\n"
			content += "  " + LocalizationManager.tr_ui("wealth") + ": " + str(faction_config.get("wealth", [0, 0])) + "\n\n"
	
	return content

func generate_animal_demo(config_data: Dictionary) -> String:
	var content = "[b]" + LocalizationManager.tr_ui("animals") + " / Animals:[/b]\n\n"
	
	var animal_keys = ["rabbit", "deer", "wolf", "bear", "chicken", "cow"]
	for animal_key in animal_keys:
		if animal_key in config_data:
			var animal_config = config_data[animal_key]
			var localized_name = LocalizationManager.tr_data("animal", animal_key)
			
			content += "[b]• " + localized_name + "[/b]\n"
			content += "  " + LocalizationManager.tr_ui("type") + ": " + str(animal_config.get("type", "unknown")) + "\n"
			content += "  " + LocalizationManager.tr_ui("size") + ": " + str(animal_config.get("size", "unknown")) + "\n"
			content += "  " + LocalizationManager.tr_ui("health") + ": " + str(animal_config.get("health", 0)) + "\n"
			content += "  " + LocalizationManager.tr_ui("danger") + ": " + str(animal_config.get("danger", 0)) + "\n\n"
	
	return content

func generate_weapon_demo(config_data: Dictionary) -> String:
	var content = "[b]" + LocalizationManager.tr_ui("weapons") + " / Weapons:[/b]\n\n"
	
	var weapon_keys = ["knife", "sword", "pistol", "rifle", "sniper", "minigun"]
	for weapon_key in weapon_keys:
		if weapon_key in config_data:
			var weapon_config = config_data[weapon_key]
			var localized_name = LocalizationManager.tr_data("weapon", weapon_key)
			
			content += "[b]• " + localized_name + "[/b]\n"
			content += "  " + LocalizationManager.tr_ui("type") + ": " + str(weapon_config.get("type", "unknown")) + "\n"
			content += "  " + LocalizationManager.tr_ui("damage") + ": " + str(weapon_config.get("damage", [0, 0])) + "\n"
			content += "  " + LocalizationManager.tr_ui("range") + ": " + str(weapon_config.get("range", 0)) + "\n"
			content += "  " + LocalizationManager.tr_ui("market_value") + ": " + str(weapon_config.get("market_value", 0)) + "\n\n"
	
	return content

func generate_skill_demo(config_data: Dictionary) -> String:
	var content = "[b]" + LocalizationManager.tr_ui("skills") + " / Skills:[/b]\n\n"
	
	var skill_keys = ["shooting", "melee", "construction", "mining", "cooking", "plants"]
	for skill_key in skill_keys:
		if skill_key in config_data:
			var skill_config = config_data[skill_key]
			var localized_name = LocalizationManager.tr_data("skill", skill_key)
			
			content += "[b]• " + localized_name + "[/b]\n"
			content += "  " + LocalizationManager.tr_ui("degradation_rate") + ": " + str(skill_config.get("degradation_rate", 0)) + "\n"
			
			var related_work = skill_config.get("related_work", [])
			if related_work.size() > 0:
				content += "  " + LocalizationManager.tr_ui("related_work") + ": " + str(related_work) + "\n"
			content += "\n"
	
	return content

func generate_trait_demo(config_data: Dictionary) -> String:
	var content = "[b]" + LocalizationManager.tr_ui("traits") + " / Traits:[/b]\n\n"
	
	var trait_keys = ["brawler", "pacifist", "industrious", "lazy", "beautiful", "ugly"]
	for trait_key in trait_keys:
		if trait_key in config_data:
			var trait_config = config_data[trait_key]
			var localized_name = LocalizationManager.tr_data("trait", trait_key)
			
			content += "[b]• " + localized_name + "[/b]\n"
			content += "  " + LocalizationManager.tr_ui("type") + ": " + str(trait_config.get("type", "unknown")) + "\n"
			content += "  " + LocalizationManager.tr_ui("mood_bonus") + ": " + str(trait_config.get("mood_bonus", 0)) + "\n"
			
			var incompatible = trait_config.get("incompatible_with", [])
			if incompatible.size() > 0:
				content += "  " + LocalizationManager.tr_ui("incompatible_with") + ": " + str(incompatible) + "\n"
			content += "\n"
	
	return content

# 添加更多UI翻译到LocalizationManager
func add_demo_translations():
	if not LocalizationManager:
		return

	# 英文翻译
	var english_ui = LocalizationManager.localization_data["en"]["ui"]
	english_ui.merge({
		"tech_level": "Tech Level",
		"population": "Population",
		"military": "Military Strength",
		"wealth": "Wealth",
		"type": "Type",
		"size": "Size",
		"danger": "Danger Level",
		"damage": "Damage",
		"range": "Range",
		"market_value": "Market Value",
		"degradation_rate": "Degradation Rate",
		"related_work": "Related Work",
		"mood_bonus": "Mood Bonus",
		"incompatible_with": "Incompatible With"
	})
	
	# 中文翻译
	var chinese_ui = LocalizationManager.localization_data["zh"]["ui"]
	chinese_ui.merge({
		"tech_level": "科技等级",
		"population": "人口",
		"military": "军事力量",
		"wealth": "财富",
		"type": "类型",
		"size": "体型",
		"danger": "危险等级",
		"damage": "伤害",
		"range": "射程",
		"market_value": "市场价值",
		"degradation_rate": "衰减率",
		"related_work": "相关工作",
		"mood_bonus": "心情加成",
		"incompatible_with": "冲突特质"
	})

# 创建多语言对比表
func create_comparison_table() -> String:
	var table = "[table=3]\n"
	table += "[cell]Key[/cell][cell]English[/cell][cell]中文[/cell]\n"
	
	# 派系对比
	var faction_keys = ["tribal", "outlander", "pirate"]
	for key in faction_keys:
		table += "[cell]" + key + "[/cell]"
		
		# 英文
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		table += "[cell]" + LocalizationManager.tr_data("faction", key) + "[/cell]"

		# 中文
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		table += "[cell]" + LocalizationManager.tr_data("faction", key) + "[/cell]\n"
	
	table += "[/table]"
	return table

# 演示动态语言切换
func demo_dynamic_switching():
	var demo_text = ""
	
	# 在英文和中文之间切换显示相同内容
	for i in range(3):
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		demo_text += "Round " + str(i + 1) + " - English: " + LocalizationManager.tr_data("weapon", "sword") + "\n"

		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		demo_text += "第" + str(i + 1) + "轮 - 中文: " + LocalizationManager.tr_data("weapon", "sword") + "\n\n"
	
	return demo_text

# 测试所有翻译是否完整
func test_translation_completeness() -> Dictionary:
	var results = {
		"missing_english": [],
		"missing_chinese": [],
		"total_keys": 0
	}
	
	var test_keys = [
		{"type": "faction", "keys": ["tribal", "outlander", "pirate"]},
		{"type": "animal", "keys": ["rabbit", "deer", "wolf"]},
		{"type": "weapon", "keys": ["knife", "sword", "pistol"]},
		{"type": "skill", "keys": ["shooting", "melee", "construction"]},
		{"type": "trait", "keys": ["brawler", "pacifist", "industrious"]}
	]
	
	for test_group in test_keys:
		for key in test_group["keys"]:
			results["total_keys"] += 1
			
			# 测试英文
			LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
			var english_text = LocalizationManager.tr_data(test_group["type"], key)
			if english_text == key:  # 如果返回原键名，说明翻译缺失
				results["missing_english"].append(test_group["type"] + "." + key)

			# 测试中文
			LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
			var chinese_text = LocalizationManager.tr_data(test_group["type"], key)
			if chinese_text == key:  # 如果返回原键名，说明翻译缺失
				results["missing_chinese"].append(test_group["type"] + "." + key)
	
	return results

# 按钮事件处理
func _on_back_button_pressed():
	# 返回主菜单
	get_tree().change_scene_to_file("res://scenes/SimpleMain.tscn")

func _on_export_button_pressed():
	# 导出翻译数据
	export_translation_data()

func export_translation_data():
	var export_data = {
		"export_time": Time.get_datetime_string_from_system(),
		"languages": {},
		"statistics": {}
	}

	# 导出所有语言的翻译数据
	if LocalizationManager:
		for language in LocalizationManager.get_available_languages():
			var lang_key = LocalizationManager.get_language_key(language)
			var lang_name = LocalizationManager.get_language_name(language)

			export_data["languages"][lang_key] = {
				"name": lang_name,
				"data": LocalizationManager.localization_data.get(lang_key, {})
			}

	# 添加统计信息
	var completeness = test_translation_completeness()
	export_data["statistics"] = completeness

	# 保存到文件
	var file_path = "user://translation_export_" + Time.get_datetime_string_from_system().replace(":", "-").replace(" ", "_") + ".json"
	if Utils.save_json(export_data, file_path):
		print("Translation data exported to: ", file_path)
	else:
		print("Failed to export translation data")

# 创建语言对比演示
func create_language_comparison_demo():
	var demo_content = "[center][b]语言对比演示 / Language Comparison Demo[/b][/center]\n\n"

	var test_items = [
		{"type": "faction", "key": "tribal"},
		{"type": "faction", "key": "pirate"},
		{"type": "animal", "key": "wolf"},
		{"type": "weapon", "key": "sword"},
		{"type": "skill", "key": "shooting"},
		{"type": "trait", "key": "beautiful"}
	]

	demo_content += "[table=4]\n"
	demo_content += "[cell][b]Type[/b][/cell][cell][b]Key[/b][/cell][cell][b]English[/b][/cell][cell][b]中文[/b][/cell]\n"

	for item in test_items:
		demo_content += "[cell]" + item["type"] + "[/cell]"
		demo_content += "[cell]" + item["key"] + "[/cell]"

		# 获取英文翻译
		LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)
		var english_text = LocalizationManager.tr_data(item["type"], item["key"])
		demo_content += "[cell]" + english_text + "[/cell]"

		# 获取中文翻译
		LocalizationManager.set_language(LocalizationManager.Language.CHINESE)
		var chinese_text = LocalizationManager.tr_data(item["type"], item["key"])
		demo_content += "[cell]" + chinese_text + "[/cell]\n"

	demo_content += "[/table]\n\n"

	# 恢复原语言
	LocalizationManager.set_language(LocalizationManager.Language.ENGLISH)

	return demo_content
