[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("GridOverlay"), PackedStringArray("Transform"), NodePath("UI/TestPanel"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/Title"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/GridToggle"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/OccupationToggle"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/PlaceBuilding"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/RemoveBuilding"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/ClearAll"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/HSeparator2"), PackedStringArray("Layout"), NodePath("UI/TestPanel/VBoxContainer/InfoLabel"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
