class_name CombatSystem
extends Node

# 战斗系统
# 管理殖民者和敌人之间的战斗

signal combat_started(attacker: Node, target: Node)
signal combat_ended(winner: Node, loser: Node)
signal damage_dealt(attacker: Node, target: Node, damage: float)

var active_combats: Dictionary = {}
var combat_id_counter: int = 0

# 武器类型
enum WeaponType {
	FISTS,
	KNIFE,
	SWORD,
	SPEAR,
	BOW,
	RIFLE
}

# 护甲类型
enum ArmorType {
	NONE,
	LEATHER,
	METAL,
	POWER_ARMOR
}

func _ready():
	name = "CombatSystem"

func _process(delta: float):
	update_active_combats(delta)

func start_combat(attacker: Node, target: Node) -> int:
	if not can_start_combat(attacker, target):
		return -1
	
	var combat_id = combat_id_counter
	combat_id_counter += 1
	
	var combat_data = {
		"id": combat_id,
		"attacker": attacker,
		"target": target,
		"start_time": Time.get_time_dict_from_system(),
		"last_attack_time": 0.0,
		"attack_cooldown": get_attack_cooldown(attacker)
	}
	
	active_combats[combat_id] = combat_data
	combat_started.emit(attacker, target)
	
	return combat_id

func can_start_combat(attacker: Node, target: Node) -> bool:
	if not attacker or not target:
		return false
	
	if attacker == target:
		return false
	
	if not attacker.has_method("get_combat_stats") or not target.has_method("get_combat_stats"):
		return false
	
	# 检查是否已经在战斗中
	for combat in active_combats.values():
		if combat.attacker == attacker or combat.target == attacker:
			return false
		if combat.attacker == target or combat.target == target:
			return false
	
	return true

func update_active_combats(delta: float):
	var combats_to_remove = []
	
	for combat_id in active_combats.keys():
		var combat = active_combats[combat_id]
		combat.last_attack_time += delta
		
		if combat.last_attack_time >= combat.attack_cooldown:
			if process_attack(combat):
				combat.last_attack_time = 0.0
				combat.attack_cooldown = get_attack_cooldown(combat.attacker)
			else:
				combats_to_remove.append(combat_id)
	
	# 移除结束的战斗
	for combat_id in combats_to_remove:
		end_combat(combat_id)

func process_attack(combat: Dictionary) -> bool:
	var attacker = combat.attacker
	var target = combat.target
	
	if not attacker or not target:
		return false
	
	if not is_instance_valid(attacker) or not is_instance_valid(target):
		return false
	
	var attacker_stats = attacker.get_combat_stats()
	var target_stats = target.get_combat_stats()
	
	# 计算命中率
	var hit_chance = calculate_hit_chance(attacker_stats, target_stats)
	
	if randf() <= hit_chance:
		# 命中，计算伤害
		var damage = calculate_damage(attacker_stats, target_stats)
		apply_damage(target, damage, attacker)
		damage_dealt.emit(attacker, target, damage)
		
		# 检查目标是否死亡
		if target_stats.health <= 0:
			combat_ended.emit(attacker, target)
			return false
	
	# 检查战斗是否应该继续
	return should_continue_combat(attacker, target)

func calculate_hit_chance(attacker_stats: Dictionary, target_stats: Dictionary) -> float:
	var base_hit_chance = 0.7
	var skill_modifier = (attacker_stats.get("combat_skill", 5) - target_stats.get("dodge_skill", 5)) * 0.05
	var weapon_modifier = get_weapon_accuracy_modifier(attacker_stats.get("weapon_type", WeaponType.FISTS))
	
	return clamp(base_hit_chance + skill_modifier + weapon_modifier, 0.1, 0.95)

func calculate_damage(attacker_stats: Dictionary, target_stats: Dictionary) -> float:
	var base_damage = attacker_stats.get("strength", 5) * 2
	var weapon_damage = get_weapon_damage(attacker_stats.get("weapon_type", WeaponType.FISTS))
	var armor_reduction = get_armor_protection(target_stats.get("armor_type", ArmorType.NONE))
	
	var total_damage = base_damage + weapon_damage
	var final_damage = max(1.0, total_damage - armor_reduction)
	
	# 添加随机性
	final_damage *= randf_range(0.8, 1.2)
	
	return final_damage

func get_weapon_damage(weapon_type: WeaponType) -> float:
	match weapon_type:
		WeaponType.FISTS: return 2.0
		WeaponType.KNIFE: return 8.0
		WeaponType.SWORD: return 15.0
		WeaponType.SPEAR: return 12.0
		WeaponType.BOW: return 10.0
		WeaponType.RIFLE: return 25.0
		_: return 2.0

func get_weapon_accuracy_modifier(weapon_type: WeaponType) -> float:
	match weapon_type:
		WeaponType.FISTS: return 0.1
		WeaponType.KNIFE: return 0.05
		WeaponType.SWORD: return 0.0
		WeaponType.SPEAR: return -0.05
		WeaponType.BOW: return -0.1
		WeaponType.RIFLE: return 0.15
		_: return 0.0

func get_armor_protection(armor_type: ArmorType) -> float:
	match armor_type:
		ArmorType.NONE: return 0.0
		ArmorType.LEATHER: return 3.0
		ArmorType.METAL: return 8.0
		ArmorType.POWER_ARMOR: return 15.0
		_: return 0.0

func get_attack_cooldown(attacker: Node) -> float:
	if not attacker.has_method("get_combat_stats"):
		return 2.0
	
	var stats = attacker.get_combat_stats()
	var weapon_type = stats.get("weapon_type", WeaponType.FISTS)
	
	match weapon_type:
		WeaponType.FISTS: return 1.0
		WeaponType.KNIFE: return 1.2
		WeaponType.SWORD: return 1.8
		WeaponType.SPEAR: return 2.0
		WeaponType.BOW: return 2.5
		WeaponType.RIFLE: return 3.0
		_: return 2.0

func apply_damage(target: Node, damage: float, _attacker: Node):
	if target.has_method("take_damage"):
		target.take_damage(damage)
	
	# 可能造成伤势
	if damage > 10 and randf() < 0.3:
		var medical_system = get_node_or_null("/root/GameManager/MedicalSystem")
		if medical_system:
			var injury_type = MedicalSystem.InjuryType.MINOR_CUT
			if damage > 20:
				injury_type = MedicalSystem.InjuryType.DEEP_WOUND
			medical_system.add_injury(target, injury_type, damage / 20.0)

func should_continue_combat(attacker: Node, target: Node) -> bool:
	if not attacker or not target:
		return false
	
	if not is_instance_valid(attacker) or not is_instance_valid(target):
		return false
	
	# 检查距离
	var distance = attacker.global_position.distance_to(target.global_position)
	if distance > 100:  # 如果距离太远，停止战斗
		return false
	
	# 检查健康状态
	var attacker_stats = attacker.get_combat_stats()
	var target_stats = target.get_combat_stats()
	
	if attacker_stats.health <= 0 or target_stats.health <= 0:
		return false
	
	return true

func end_combat(combat_id: int):
	if active_combats.has(combat_id):
		var combat = active_combats[combat_id]
		active_combats.erase(combat_id)
		
		# 通知战斗结束
		if is_instance_valid(combat.attacker) and is_instance_valid(combat.target):
			var attacker_stats = combat.attacker.get_combat_stats()
			var target_stats = combat.target.get_combat_stats()
			
			var winner = combat.attacker if attacker_stats.health > target_stats.health else combat.target
			var loser = combat.target if winner == combat.attacker else combat.attacker
			
			combat_ended.emit(winner, loser)

func get_combat_status(entity: Node) -> Dictionary:
	for combat in active_combats.values():
		if combat.attacker == entity:
			return {
				"in_combat": true,
				"role": "attacker",
				"opponent": combat.target,
				"time_remaining": combat.attack_cooldown - combat.last_attack_time
			}
		elif combat.target == entity:
			return {
				"in_combat": true,
				"role": "defender", 
				"opponent": combat.attacker,
				"time_remaining": combat.attack_cooldown - combat.last_attack_time
			}
	
	return {"in_combat": false}

func force_end_combat(entity: Node):
	var combats_to_remove = []
	
	for combat_id in active_combats.keys():
		var combat = active_combats[combat_id]
		if combat.attacker == entity or combat.target == entity:
			combats_to_remove.append(combat_id)
	
	for combat_id in combats_to_remove:
		end_combat(combat_id)

func get_active_combat_count() -> int:
	return active_combats.size()
