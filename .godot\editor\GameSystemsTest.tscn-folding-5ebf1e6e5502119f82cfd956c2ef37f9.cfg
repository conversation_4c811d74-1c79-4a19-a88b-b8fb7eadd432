[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("UI/MainPanel"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/Title"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/HSeparator"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestMedical"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestCombat"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestWork"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestMood"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestResearch"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestTrade"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestEvent"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/SystemTests/TestFaction"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/HSeparator2"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/RunAllTests"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/ClearLog"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/LeftPanel/ControlButtons/ExitTest"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/RightPanel"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/RightPanel/LogTitle"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/RightPanel/HSeparator3"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/RightPanel/LogContainer"), PackedStringArray("Layout"), NodePath("UI/MainPanel/HBoxContainer/RightPanel/LogContainer/LogText"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
