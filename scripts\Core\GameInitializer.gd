extends Node
class_name GameInitializer

# 游戏初始化器 - 设置初始游戏状态

@export var initial_colonist_count: int = 3
@export var starting_resources: Dictionary = {
	"food": 150,
	"wood": 100,
	"stone": 50,
	"steel": 20,
	"medicine": 10
}

var colonist_scene = preload("res://scenes/SimpleColonist.tscn")
# 注意：Building场景暂时不存在，先注释掉相关功能

func _ready():
	# 等待一帧确保所有节点都已准备好
	await get_tree().process_frame
	initialize_game()

func initialize_game():
	print("Initializing RimWorld-like game...")
	
	# 设置初始资源
	setup_initial_resources()
	
	# 生成初始殖民者
	spawn_initial_colonists()
	
	# 创建一些初始建筑
	create_initial_buildings()
	
	print("Game initialization completed!")

func setup_initial_resources():
	if not GameManager.instance:
		print("Warning: GameManager not found!")
		return
	
	# 重置资源为初始值
	for resource_type in starting_resources:
		var current_amount = GameManager.instance.get_resource(resource_type)
		var target_amount = starting_resources[resource_type]
		var difference = target_amount - current_amount
		
		if difference > 0:
			GameManager.instance.add_resource(resource_type, difference)
		elif difference < 0:
			GameManager.instance.consume_resource(resource_type, -difference)
	
	print("Initial resources set up")

func spawn_initial_colonists():
	var spawn_positions = [
		Vector2(100, 100),
		Vector2(150, 120),
		Vector2(80, 140)
	]
	
	for i in range(min(initial_colonist_count, spawn_positions.size())):
		var colonist = colonist_scene.instantiate()
		
		# 设置殖民者位置
		colonist.global_position = spawn_positions[i]
		
		# 随机化殖民者属性
		randomize_colonist(colonist)
		
		# 添加到场景
		get_tree().current_scene.get_node("ColonistContainer").add_child(colonist)
		
		# 注册到游戏管理器
		GameManager.instance.add_colonist(colonist)
		
		print("Spawned colonist: ", colonist.colonist_name)

func randomize_colonist(colonist: Colonist):
	# 随机化殖民者名字
	var first_names = ["Alex", "Sam", "Jordan", "Casey", "Riley", "Morgan", "Taylor", "Avery", "Blake", "Quinn"]
	var last_names = ["Smith", "Johnson", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor", "Anderson", "Thomas"]
	
	colonist.colonist_name = first_names[randi() % first_names.size()] + " " + last_names[randi() % last_names.size()]
	
	# 随机化技能
	for skill in colonist.skills:
		colonist.skills[skill] = randi_range(1, 15)
	
	# 随机化健康和心情
	colonist.current_health = randf_range(80.0, 100.0)
	colonist.current_mood = randf_range(30.0, 70.0)
	
	# 随机化需求
	for need in colonist.needs:
		colonist.needs[need] = randf_range(20.0, 80.0)

func create_initial_buildings():
	# 暂时禁用建筑创建功能，等待Building场景文件创建
	print("Building creation temporarily disabled - Building scene not available")
	# TODO: 重新启用建筑创建功能
	pass

func create_building(_building_type: Building.BuildingType, _position: Vector2):
	# 暂时禁用建筑创建功能
	print("Building creation temporarily disabled")
	pass

func create_random_events():
	# 创建随机事件系统（可选）
	var event_timer = Timer.new()
	event_timer.wait_time = 30.0  # 每30秒一个随机事件
	event_timer.timeout.connect(_on_random_event)
	event_timer.autostart = true
	add_child(event_timer)

func _on_random_event():
	# 简单的随机事件
	var events = [
		"resource_drop",
		"trader_arrival",
		"weather_change",
		"animal_migration"
	]
	
	var chosen_event = events[randi() % events.size()]
	trigger_event(chosen_event)

func trigger_event(event_type: String):
	match event_type:
		"resource_drop":
			# 资源空投
			var resource_types = ["food", "wood", "steel", "medicine"]
			var resource_type = resource_types[randi() % resource_types.size()]
			var amount = randi_range(10, 30)
			GameManager.instance.add_resource(resource_type, amount)
			print("Resource drop: +", amount, " ", resource_type)
		
		"trader_arrival":
			# 商人到达
			print("A trader has arrived!")
		
		"weather_change":
			# 天气变化
			print("Weather is changing...")
		
		"animal_migration":
			# 动物迁徙
			print("Animals are migrating through the area")

# 保存/加载功能
func save_game_state() -> Dictionary:
	var save_data = {
		"colonists": [],
		"buildings": [],
		"resources": GameManager.instance.resources,
		"game_time": GameManager.instance.game_time
	}
	
	# 保存殖民者数据
	for colonist in GameManager.instance.colonists:
		if is_instance_valid(colonist):
			save_data["colonists"].append({
				"name": colonist.colonist_name,
				"position": colonist.global_position,
				"health": colonist.current_health,
				"mood": colonist.current_mood,
				"skills": colonist.skills,
				"needs": colonist.needs
			})
	
	# 保存建筑数据
	for building in GameManager.instance.buildings:
		if is_instance_valid(building):
			save_data["buildings"].append({
				"type": building.building_type,
				"position": building.global_position,
				"health": building.current_health,
				"state": building.current_state
			})
	
	return save_data

func load_game_state(save_data: Dictionary):
	# 清除现有对象
	clear_game_objects()
	
	# 加载资源
	if "resources" in save_data:
		GameManager.instance.resources = save_data["resources"]
	
	# 加载游戏时间
	if "game_time" in save_data:
		GameManager.instance.game_time = save_data["game_time"]
	
	# 加载殖民者
	if "colonists" in save_data:
		for colonist_data in save_data["colonists"]:
			load_colonist(colonist_data)
	
	# 加载建筑
	if "buildings" in save_data:
		for building_data in save_data["buildings"]:
			load_building(building_data)

func clear_game_objects():
	# 清除所有殖民者
	for colonist in GameManager.instance.colonists.duplicate():
		if is_instance_valid(colonist):
			colonist.queue_free()
	GameManager.instance.colonists.clear()
	
	# 清除所有建筑
	for building in GameManager.instance.buildings.duplicate():
		if is_instance_valid(building):
			building.queue_free()
	GameManager.instance.buildings.clear()

func load_colonist(data: Dictionary):
	var colonist = colonist_scene.instantiate()
	colonist.colonist_name = data.get("name", "Unknown")
	colonist.global_position = data.get("position", Vector2.ZERO)
	colonist.current_health = data.get("health", 100.0)
	colonist.current_mood = data.get("mood", 50.0)
	colonist.skills = data.get("skills", {})
	colonist.needs = data.get("needs", {})
	
	get_tree().current_scene.get_node("ColonistContainer").add_child(colonist)
	GameManager.instance.add_colonist(colonist)

func load_building(_data: Dictionary):
	# 暂时禁用建筑加载功能
	print("Building loading temporarily disabled")
	pass
