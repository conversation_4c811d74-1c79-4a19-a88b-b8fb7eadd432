extends RefCounted
class_name DataGenerator

# 数据生成器 - 动态生成游戏内容

# 名字生成器
static var NAME_PARTS = {
	"first_names_male": [
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Xavier", "<PERSON>"
	],
	"first_names_female": [
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
	],
	"last_names": [
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Fisher", "Green", "Hall",
		"Johnson", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
	],
	"faction_prefixes": [
		"<PERSON>", "<PERSON>", "<PERSON>", "Great", "Lost", "Free", "Wild", "Dark", "Red", "Blue"
	],
	"faction_suffixes": [
		"Republic", "Empire", "Union", "Alliance", "Coalition", "Federation", "Tribe",
		"Clan", "Brotherhood", "Order", "Guild", "Company", "Corporation", "Syndicate"
	]
}

# 随机名字生成
static func generate_colonist_name(gender: String = "") -> String:
	var first_names = []
	if gender == "male":
		first_names = NAME_PARTS["first_names_male"]
	elif gender == "female":
		first_names = NAME_PARTS["first_names_female"]
	else:
		first_names = NAME_PARTS["first_names_male"] + NAME_PARTS["first_names_female"]
	
	var first_name = Utils.random_choice(first_names)
	var last_name = Utils.random_choice(NAME_PARTS["last_names"])
	return first_name + " " + last_name

static func generate_faction_name() -> String:
	var prefix = Utils.random_choice(NAME_PARTS["faction_prefixes"])
	var suffix = Utils.random_choice(NAME_PARTS["faction_suffixes"])
	return prefix + " " + suffix

# 随机特质生成
static func generate_random_traits(count: int = 2) -> Array[String]:
	var all_traits = GameConfig.get_config("trait").keys()
	var selected_traits: Array[String] = []
	var attempts = 0

	while selected_traits.size() < count and attempts < 20:
		var _trait = Utils.random_choice(all_traits)
		var trait_config = GameConfig.get_config("trait", _trait)

		# 检查冲突特质
		var has_conflict = false
		for existing_trait in selected_traits:
			var existing_config = GameConfig.get_config("trait", existing_trait)
			if _trait in existing_config.get("incompatible_with", []):
				has_conflict = true
				break

		if not has_conflict and _trait not in selected_traits:
			selected_traits.append(_trait)

		attempts += 1

	return selected_traits

# 随机技能生成
static func generate_random_skills() -> Dictionary:
	var skills = {}
	var skill_names = GameConfig.get_config("skill").keys()
	
	for skill_name in skill_names:
		# 生成0-20的技能等级，偏向中等水平
		var skill_level = int(randf_range(0, 20))
		if randf() < 0.3:  # 30%概率有激情
			var passion_level = randi_range(1, 2)  # 1=小激情, 2=大激情
			skills[skill_name] = {"level": skill_level, "passion": passion_level}
		else:
			skills[skill_name] = {"level": skill_level, "passion": 0}
	
	return skills

# 随机背景故事生成
static func generate_backstory() -> Dictionary:
	var childhood_stories = [
		"Abandoned Child", "Farm Kid", "Urbworld Urchin", "Glitterworld Kid",
		"Medieval Lordling", "Tribal Child", "Vatgrown Child", "Caveworld Tender"
	]
	
	var adulthood_stories = [
		"Farmer", "Hunter", "Soldier", "Scientist", "Artist", "Trader", "Pirate",
		"Noble", "Slave", "Mercenary", "Engineer", "Doctor", "Chef", "Miner"
	]
	
	return {
		"childhood": Utils.random_choice(childhood_stories),
		"adulthood": Utils.random_choice(adulthood_stories)
	}

# 随机装备生成
static func generate_random_equipment() -> Dictionary:
	var weapons = GameConfig.get_config("weapon").keys()
	var armors = GameConfig.get_config("armor").keys()
	
	var equipment = {
		"weapon": null,
		"armor": []
	}
	
	# 50%概率有武器
	if randf() < 0.5:
		equipment["weapon"] = Utils.random_choice(weapons)
	
	# 随机护甲
	var armor_count = randi_range(0, 3)
	for i in range(armor_count):
		var armor = Utils.random_choice(armors)
		if armor not in equipment["armor"]:
			equipment["armor"].append(armor)
	
	return equipment

# 动态事件生成
static func generate_random_event() -> Dictionary:
	var event_types = [
		"trader_arrival", "raid", "solar_flare", "toxic_fallout", "heat_wave",
		"cold_snap", "volcanic_winter", "eclipse", "aurora", "meteorite",
		"animal_migration", "disease_outbreak", "psychic_storm", "ship_crash",
		"ancient_danger", "quest_opportunity", "refugee_arrival", "manhunter_pack"
	]
	
	var event_type = Utils.random_choice(event_types)
	var event_data = {
		"type": event_type,
		"duration": randf_range(3600.0, 86400.0),  # 1小时到1天
		"severity": randf_range(0.1, 1.0),
		"description": generate_event_description(event_type)
	}
	
	return event_data

static func generate_event_description(event_type: String) -> String:
	var descriptions = {
		"trader_arrival": "A friendly trader has arrived at your colony.",
		"raid": "Hostile forces are approaching your colony!",
		"solar_flare": "A solar flare is disrupting all electrical equipment.",
		"toxic_fallout": "Toxic chemicals are falling from the sky.",
		"heat_wave": "An extreme heat wave is affecting the region.",
		"cold_snap": "Temperatures have dropped to dangerous levels.",
		"volcanic_winter": "Ash from a distant volcano blocks the sun.",
		"eclipse": "The moon passes in front of the sun.",
		"aurora": "Beautiful lights dance across the sky.",
		"meteorite": "A meteorite has crashed nearby!",
		"animal_migration": "A herd of animals is migrating through the area.",
		"disease_outbreak": "A contagious disease is spreading.",
		"psychic_storm": "Psychic energy is affecting everyone's minds.",
		"ship_crash": "A spaceship has crashed in the vicinity.",
		"ancient_danger": "Something ancient and dangerous has awakened.",
		"quest_opportunity": "An opportunity for adventure presents itself.",
		"refugee_arrival": "Refugees are seeking shelter at your colony.",
		"manhunter_pack": "A pack of enraged animals is on the hunt!"
	}
	
	return descriptions.get(event_type, "Something interesting is happening.")

# 随机地形生成
static func generate_terrain_features() -> Array:
	var features = []
	var feature_types = [
		"hill", "mountain", "river", "lake", "forest", "plains", "swamp",
		"desert", "cave", "ruins", "crater", "geyser", "hot_spring"
	]
	
	var feature_count = randi_range(3, 8)
	for i in range(feature_count):
		var feature = {
			"type": Utils.random_choice(feature_types),
			"position": Vector2(randf_range(0, 1000), randf_range(0, 1000)),
			"size": randf_range(50, 200),
			"resources": generate_terrain_resources()
		}
		features.append(feature)
	
	return features

static func generate_terrain_resources() -> Dictionary:
	var resources = {}
	var resource_types = ["steel", "gold", "silver", "uranium", "jade", "stone"]
	
	for resource_type in resource_types:
		if randf() < 0.3:  # 30%概率有这种资源
			resources[resource_type] = randi_range(100, 1000)
	
	return resources

# 随机任务生成
static func generate_random_quest() -> Dictionary:
	var quest_types = [
		"rescue_mission", "trade_caravan", "artifact_hunt", "diplomatic_mission",
		"exploration", "construction_help", "medical_emergency", "research_project"
	]
	
	var quest_type = Utils.random_choice(quest_types)
	var quest = {
		"type": quest_type,
		"title": generate_quest_title(quest_type),
		"description": generate_quest_description(quest_type),
		"duration": randf_range(86400.0, 604800.0),  # 1-7天
		"rewards": generate_quest_rewards(),
		"requirements": generate_quest_requirements(quest_type),
		"difficulty": randf_range(0.1, 1.0)
	}
	
	return quest

static func generate_quest_title(quest_type: String) -> String:
	var titles = {
		"rescue_mission": "Rescue the Lost Colonist",
		"trade_caravan": "Escort the Trade Caravan",
		"artifact_hunt": "Recover the Ancient Artifact",
		"diplomatic_mission": "Diplomatic Negotiations",
		"exploration": "Explore the Unknown Region",
		"construction_help": "Construction Assistance",
		"medical_emergency": "Medical Crisis Response",
		"research_project": "Scientific Collaboration"
	}
	
	return titles.get(quest_type, "Unknown Quest")

static func generate_quest_description(quest_type: String) -> String:
	var descriptions = {
		"rescue_mission": "A colonist has gone missing in dangerous territory. Find and rescue them.",
		"trade_caravan": "A valuable trade caravan needs protection on their journey.",
		"artifact_hunt": "Ancient ruins contain a powerful artifact. Retrieve it safely.",
		"diplomatic_mission": "Negotiate a peace treaty with a hostile faction.",
		"exploration": "Map an unexplored region and report your findings.",
		"construction_help": "Help another settlement with a major construction project.",
		"medical_emergency": "A plague has struck a nearby settlement. Provide medical aid.",
		"research_project": "Collaborate on an important scientific research project."
	}
	
	return descriptions.get(quest_type, "Complete this mysterious quest.")

static func generate_quest_rewards() -> Dictionary:
	var possible_rewards = ["silver", "steel", "components", "medicine", "food"]
	var rewards = {}
	
	var reward_count = randi_range(1, 3)
	for i in range(reward_count):
		var reward_type = Utils.random_choice(possible_rewards)
		var amount = randi_range(50, 500)
		rewards[reward_type] = rewards.get(reward_type, 0) + amount
	
	return rewards

static func generate_quest_requirements(quest_type: String) -> Dictionary:
	var requirements = {}
	
	match quest_type:
		"rescue_mission":
			requirements = {"colonists": 2, "weapons": 1}
		"trade_caravan":
			requirements = {"colonists": 3, "vehicles": 1}
		"artifact_hunt":
			requirements = {"colonists": 4, "equipment": ["mining_gear"]}
		"diplomatic_mission":
			requirements = {"colonists": 1, "social_skill": 8}
		"exploration":
			requirements = {"colonists": 2, "supplies": 100}
		"construction_help":
			requirements = {"colonists": 3, "construction_skill": 6}
		"medical_emergency":
			requirements = {"colonists": 2, "medicine": 50, "medical_skill": 7}
		"research_project":
			requirements = {"colonists": 1, "research_skill": 10}
	
	return requirements

# 随机灾难生成
static func generate_random_disaster() -> Dictionary:
	var disaster_types = [
		"earthquake", "flood", "wildfire", "tornado", "blizzard", "drought",
		"volcanic_eruption", "meteor_shower", "plague_outbreak", "robot_uprising"
	]
	
	var disaster_type = Utils.random_choice(disaster_types)
	var disaster = {
		"type": disaster_type,
		"severity": randf_range(0.3, 1.0),
		"duration": randf_range(3600.0, 172800.0),  # 1小时到2天
		"affected_area": randf_range(100, 500),
		"warning_time": randf_range(0, 3600.0),  # 0-1小时预警
		"effects": generate_disaster_effects(disaster_type)
	}
	
	return disaster

static func generate_disaster_effects(disaster_type: String) -> Dictionary:
	var effects = {}
	
	match disaster_type:
		"earthquake":
			effects = {"building_damage": 0.3, "mood_penalty": -10}
		"flood":
			effects = {"crop_damage": 0.8, "movement_penalty": 0.5}
		"wildfire":
			effects = {"fire_spread": 0.9, "air_quality": -0.5}
		"tornado":
			effects = {"building_damage": 0.6, "debris_scatter": true}
		"blizzard":
			effects = {"temperature": -20, "visibility": 0.2}
		"drought":
			effects = {"crop_growth": 0.3, "water_shortage": true}
		"volcanic_eruption":
			effects = {"ash_fall": true, "temperature": -5, "air_quality": -0.8}
		"meteor_shower":
			effects = {"random_damage": 0.2, "rare_materials": true}
		"plague_outbreak":
			effects = {"disease_spread": 0.7, "quarantine_needed": true}
		"robot_uprising":
			effects = {"ai_hostility": 0.9, "electronic_disruption": true}
	
	return effects

# 批量数据生成
static func generate_colonist_batch(count: int) -> Array:
	var colonists = []
	for i in range(count):
		var colonist_data = {
			"name": generate_colonist_name(),
			"age": randi_range(18, 80),
			"traits": generate_random_traits(),
			"skills": generate_random_skills(),
			"backstory": generate_backstory(),
			"equipment": generate_random_equipment(),
			"health": randf_range(0.8, 1.0),
			"mood": randf_range(30, 80)
		}
		colonists.append(colonist_data)
	
	return colonists

static func generate_world_data() -> Dictionary:
	return {
		"terrain_features": generate_terrain_features(),
		"weather_patterns": generate_weather_patterns(),
		"wildlife_populations": generate_wildlife_data(),
		"resource_deposits": generate_resource_deposits(),
		"points_of_interest": generate_points_of_interest()
	}

static func generate_weather_patterns() -> Array:
	var patterns = []
	var weather_types = GameConfig.get_config("weather").keys()
	
	for i in range(7):  # 一周的天气
		var weather = {
			"day": i,
			"type": Utils.random_choice(weather_types),
			"intensity": randf_range(0.3, 1.0),
			"duration": randf_range(3600.0, 43200.0)  # 1-12小时
		}
		patterns.append(weather)
	
	return patterns

static func generate_wildlife_data() -> Dictionary:
	var wildlife = {}
	var animal_types = GameConfig.get_config("animal").keys()
	
	for animal_type in animal_types:
		wildlife[animal_type] = {
			"population": randi_range(5, 50),
			"migration_pattern": Utils.random_choice(["sedentary", "seasonal", "nomadic"]),
			"aggression_level": randf_range(0.0, 1.0)
		}
	
	return wildlife

static func generate_resource_deposits() -> Array:
	var deposits = []
	var resource_types = ["steel", "gold", "silver", "uranium", "jade", "stone", "coal", "oil"]
	
	var deposit_count = randi_range(5, 15)
	for i in range(deposit_count):
		var deposit = {
			"type": Utils.random_choice(resource_types),
			"position": Vector2(randf_range(0, 1000), randf_range(0, 1000)),
			"richness": randf_range(0.3, 1.0),
			"size": randi_range(10, 100),
			"depth": randf_range(1, 10)
		}
		deposits.append(deposit)
	
	return deposits

static func generate_points_of_interest() -> Array:
	var poi_types = [
		"ancient_ruins", "crashed_ship", "abandoned_base", "natural_wonder",
		"resource_cache", "research_facility", "trading_post", "shrine"
	]
	
	var points = []
	var poi_count = randi_range(3, 10)
	
	for i in range(poi_count):
		var poi = {
			"type": Utils.random_choice(poi_types),
			"position": Vector2(randf_range(0, 1000), randf_range(0, 1000)),
			"danger_level": randf_range(0.0, 1.0),
			"rewards": generate_poi_rewards(),
			"explored": false
		}
		points.append(poi)
	
	return points

static func generate_poi_rewards() -> Dictionary:
	var rewards = {}
	var reward_types = ["artifacts", "technology", "resources", "information", "allies"]
	
	var reward_count = randi_range(1, 3)
	for i in range(reward_count):
		var reward_type = Utils.random_choice(reward_types)
		rewards[reward_type] = randi_range(1, 10)
	
	return rewards
