[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("World/GridOverlay"), PackedStringArray("Transform"), NodePath("UI/TopPanel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/Title"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/VSeparator"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/StatsContainer"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/StatsContainer/ColonistStats"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/StatsContainer/BuildingStats"), PackedStringArray("Layout"), NodePath("UI/TopPanel/HBoxContainer/StatsContainer/SystemStats"), PackedStringArray("Layout"), NodePath("UI/LeftPanel"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/TestControls"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/SpawnColonist"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/PlaceBuilding"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/SpawnAnimal"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/HSeparator2"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/TestScenarios"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/TestRaid"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/TestTrade"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/TestEvent"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/HSeparator3"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/DisplayControls"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/ToggleGrid"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/ToggleUI"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/HSeparator4"), PackedStringArray("Layout"), NodePath("UI/LeftPanel/VBoxContainer/ExitTest"), PackedStringArray("Layout"), NodePath("UI/RightPanel"), PackedStringArray("Layout"), NodePath("UI/RightPanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("UI/RightPanel/VBoxContainer/LogTitle"), PackedStringArray("Layout"), NodePath("UI/RightPanel/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("UI/RightPanel/VBoxContainer/LogContainer"), PackedStringArray("Layout"), NodePath("UI/RightPanel/VBoxContainer/LogContainer/LogText"), PackedStringArray("Layout"), NodePath("UI/BottomPanel"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/HBoxContainer"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/HBoxContainer/InfoLabel"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
