extends Control
class_name AdvancedSystemsUI

# 高级系统管理界面 - 管理所有新增的高级功能

@onready var tab_container: TabContainer = $TabContainer

# 各个系统的UI面板
@onready var mood_panel: Control = $TabContainer/Mood
@onready var medical_panel: Control = $TabContainer/Medical
@onready var combat_panel: Control = $TabContainer/Combat
@onready var work_panel: Control = $TabContainer/Work
@onready var room_panel: Control = $TabContainer/Rooms
@onready var season_panel: Control = $TabContainer/Season
@onready var story_panel: Control = $TabContainer/Stories

# 心情系统UI元素
@onready var colonist_mood_list: ItemList = $TabContainer/Mood/VBoxContainer/ColonistMoodList
@onready var mood_details: RichTextLabel = $TabContainer/Mood/VBoxContainer/MoodDetails
@onready var relationship_matrix: GridContainer = $TabContainer/Mood/VBoxContainer/RelationshipMatrix

# 医疗系统UI元素
@onready var health_overview: ItemList = $TabContainer/Medical/VBoxContainer/HealthOverview
@onready var injury_details: RichTextLabel = $TabContainer/Medical/VBoxContainer/InjuryDetails
@onready var surgery_options: VBoxContainer = $TabContainer/Medical/VBoxContainer/SurgeryOptions

# 战斗系统UI元素
@onready var combat_status: RichTextLabel = $TabContainer/Combat/VBoxContainer/CombatStatus
@onready var combatant_list: ItemList = $TabContainer/Combat/VBoxContainer/CombatantList
@onready var force_combat_button: Button = $TabContainer/Combat/VBoxContainer/ForceCombatButton

# 工作系统UI元素
@onready var work_queue: ItemList = $TabContainer/Work/VBoxContainer/WorkQueue
@onready var colonist_work_list: ItemList = $TabContainer/Work/VBoxContainer/ColonistWorkList
@onready var work_priorities: GridContainer = $TabContainer/Work/VBoxContainer/WorkPriorities

# 房间系统UI元素
@onready var room_list: ItemList = $TabContainer/Rooms/VBoxContainer/RoomList
@onready var room_details: RichTextLabel = $TabContainer/Rooms/VBoxContainer/RoomDetails
@onready var room_stats: RichTextLabel = $TabContainer/Rooms/VBoxContainer/RoomStats

# 季节系统UI元素
@onready var season_info: RichTextLabel = $TabContainer/Season/VBoxContainer/SeasonInfo
@onready var weather_forecast: RichTextLabel = $TabContainer/Season/VBoxContainer/WeatherForecast
@onready var temperature_graph: Control = $TabContainer/Season/VBoxContainer/TemperatureGraph

# 故事系统UI元素
@onready var active_stories: ItemList = $TabContainer/Stories/VBoxContainer/ActiveStories
@onready var story_details: RichTextLabel = $TabContainer/Stories/VBoxContainer/StoryDetails
@onready var story_history: ItemList = $TabContainer/Stories/VBoxContainer/StoryHistory

# 新增系统UI元素
@onready var faction_panel: Control = $TabContainer/Factions
@onready var vehicle_panel: Control = $TabContainer/Vehicles
@onready var pollution_panel: Control = $TabContainer/Environment
@onready var religion_panel: Control = $TabContainer/Religion
@onready var genetic_panel: Control = $TabContainer/Genetics
@onready var ai_panel: Control = $TabContainer/AI

# 派系系统UI元素
@onready var faction_list: ItemList = $TabContainer/Factions/VBoxContainer/FactionList
@onready var faction_details: RichTextLabel = $TabContainer/Factions/VBoxContainer/FactionDetails
@onready var diplomatic_actions: VBoxContainer = $TabContainer/Factions/VBoxContainer/DiplomaticActions

# 载具系统UI元素
@onready var vehicle_list: ItemList = $TabContainer/Vehicles/VBoxContainer/VehicleList
@onready var vehicle_details: RichTextLabel = $TabContainer/Vehicles/VBoxContainer/VehicleDetails
@onready var vehicle_missions: ItemList = $TabContainer/Vehicles/VBoxContainer/VehicleMissions

# 环境系统UI元素
@onready var pollution_overview: RichTextLabel = $TabContainer/Environment/VBoxContainer/PollutionOverview
@onready var ecosystem_list: ItemList = $TabContainer/Environment/VBoxContainer/EcosystemList
@onready var environmental_actions: VBoxContainer = $TabContainer/Environment/VBoxContainer/EnvironmentalActions

# 宗教系统UI元素
@onready var religion_list: ItemList = $TabContainer/Religion/VBoxContainer/ReligionList
@onready var religion_details: RichTextLabel = $TabContainer/Religion/VBoxContainer/ReligionDetails
@onready var religious_actions: VBoxContainer = $TabContainer/Religion/VBoxContainer/ReligiousActions

# 基因系统UI元素
@onready var genetic_profiles: ItemList = $TabContainer/Genetics/VBoxContainer/GeneticProfiles
@onready var genetic_details: RichTextLabel = $TabContainer/Genetics/VBoxContainer/GeneticDetails
@onready var genetic_experiments: ItemList = $TabContainer/Genetics/VBoxContainer/GeneticExperiments

# AI系统UI元素
@onready var ai_units_list: ItemList = $TabContainer/AI/VBoxContainer/AIUnitsList
@onready var ai_details: RichTextLabel = $TabContainer/AI/VBoxContainer/AIDetails
@onready var ai_controls: VBoxContainer = $TabContainer/AI/VBoxContainer/AIControls

var selected_colonist: Colonist = null
var selected_room = null
var update_timer: float = 0.0
var update_interval: float = 2.0

func _ready():
	setup_ui()
	connect_signals()

func setup_ui():
	# 设置标签页
	tab_container.set_tab_title(0, "Mood & Relations")
	tab_container.set_tab_title(1, "Medical")
	tab_container.set_tab_title(2, "Combat")
	tab_container.set_tab_title(3, "Work")
	tab_container.set_tab_title(4, "Rooms")
	tab_container.set_tab_title(5, "Season")
	tab_container.set_tab_title(6, "Stories")
	tab_container.set_tab_title(7, "Factions")
	tab_container.set_tab_title(8, "Vehicles")
	tab_container.set_tab_title(9, "Environment")
	tab_container.set_tab_title(10, "Religion")
	tab_container.set_tab_title(11, "Genetics")
	tab_container.set_tab_title(12, "AI")
	
	# 连接UI事件
	colonist_mood_list.item_selected.connect(_on_colonist_mood_selected)
	health_overview.item_selected.connect(_on_health_colonist_selected)
	combatant_list.item_selected.connect(_on_combatant_selected)
	colonist_work_list.item_selected.connect(_on_work_colonist_selected)
	room_list.item_selected.connect(_on_room_selected)
	active_stories.item_selected.connect(_on_story_selected)
	force_combat_button.pressed.connect(_on_force_combat_pressed)

func connect_signals():
	if GameManager.instance:
		# 连接系统信号
		if GameManager.instance.mood_system:
			GameManager.instance.mood_system.mood_changed.connect(_on_mood_changed)
			GameManager.instance.mood_system.mental_break.connect(_on_mental_break)
		
		if GameManager.instance.medical_system:
			GameManager.instance.medical_system.injury_sustained.connect(_on_injury_sustained)
		
		if GameManager.instance.storyteller_system:
			GameManager.instance.storyteller_system.story_event_triggered.connect(_on_story_event)

func _process(delta: float):
	update_timer += delta
	if update_timer >= update_interval:
		update_all_panels()
		update_timer = 0.0

func update_all_panels():
	match tab_container.current_tab:
		0: update_mood_panel()
		1: update_medical_panel()
		2: update_combat_panel()
		3: update_work_panel()
		4: update_room_panel()
		5: update_season_panel()
		6: update_story_panel()
		7: update_faction_panel()
		8: update_vehicle_panel()
		9: update_pollution_panel()
		10: update_religion_panel()
		11: update_genetic_panel()
		12: update_ai_panel()

func update_mood_panel():
	if not GameManager.instance or not GameManager.instance.mood_system:
		return
	
	# 更新殖民者心情列表
	colonist_mood_list.clear()
	for colonist in GameManager.instance.colonists:
		var mood_info = GameManager.instance.mood_system.get_mood_info(colonist)
		if mood_info:
			var mood_text = colonist.colonist_name + " - Mood: " + str(int(mood_info["current_mood"]))
			var mental_state = mood_info.get("mental_state", "NORMAL")
			if mental_state != "NORMAL":
				mood_text += " (" + mental_state + ")"
			colonist_mood_list.add_item(mood_text)
			colonist_mood_list.set_item_metadata(colonist_mood_list.get_item_count() - 1, colonist)

func update_medical_panel():
	if not GameManager.instance or not GameManager.instance.medical_system:
		return
	
	# 更新健康概览
	health_overview.clear()
	for colonist in GameManager.instance.colonists:
		var health_info = GameManager.instance.medical_system.get_health_info(colonist)
		if health_info:
			var health_text = colonist.colonist_name + " - Health: " + str(int(health_info["overall_health"])) + "%"
			if health_info["injuries"].size() > 0:
				health_text += " (Injured)"
			if health_info["diseases"].size() > 0:
				health_text += " (Sick)"
			health_overview.add_item(health_text)
			health_overview.set_item_metadata(health_overview.get_item_count() - 1, colonist)

func update_combat_panel():
	if not GameManager.instance or not GameManager.instance.combat_system:
		return
	
	# 更新战斗状态
	var combat_info = "Active Combats: " + str(GameManager.instance.combat_system.active_combats.size()) + "\n"
	combat_info += "Registered Combatants: " + str(GameManager.instance.combat_system.combatants.size()) + "\n"
	combat_status.text = combat_info
	
	# 更新战斗者列表
	combatant_list.clear()
	for entity in GameManager.instance.combat_system.combatants:
		if is_instance_valid(entity):
			var combat_info_entity = GameManager.instance.combat_system.get_combat_info(entity)
			var entity_name = entity.colonist_name if entity.has_method("get_name") else str(entity)
			var combat_text = entity_name + " - " + combat_info_entity.get("combat_state", "UNKNOWN")
			combatant_list.add_item(combat_text)
			combatant_list.set_item_metadata(combatant_list.get_item_count() - 1, entity)

func update_work_panel():
	if not GameManager.instance or not GameManager.instance.work_system:
		return
	
	# 更新工作队列
	work_queue.clear()
	for task in GameManager.instance.work_system.available_work_tasks:
		var work_text = WorkSystem.WorkType.keys()[task.work_type]
		if task.assigned_colonist:
			work_text += " (Assigned to " + task.assigned_colonist.colonist_name + ")"
		else:
			work_text += " (Unassigned)"
		work_queue.add_item(work_text)
	
	# 更新殖民者工作状态
	colonist_work_list.clear()
	for colonist in GameManager.instance.colonists:
		var work_info = GameManager.instance.work_system.get_colonist_work_info(colonist)
		if work_info:
			var work_text = colonist.colonist_name + " - " + work_info.get("current_work", "Idle")
			if work_info.get("needs_break", false):
				work_text += " (Needs Rest)"
			colonist_work_list.add_item(work_text)
			colonist_work_list.set_item_metadata(colonist_work_list.get_item_count() - 1, colonist)

func update_room_panel():
	if not GameManager.instance or not GameManager.instance.room_system:
		return
	
	# 更新房间列表
	room_list.clear()
	for room in GameManager.instance.room_system.rooms:
		var room_info = room.get_room_info()
		var room_text = room_info["type"] + " - " + room_info["quality"] + " (" + str(room_info["area"]) + " tiles)"
		room_list.add_item(room_text)
		room_list.set_item_metadata(room_list.get_item_count() - 1, room)
	
	# 更新房间统计
	var stats = GameManager.instance.room_system.get_room_statistics()
	var stats_text = "Total Rooms: " + str(stats["total_rooms"]) + "\n"
	stats_text += "Total Area: " + str(stats["total_area"]) + " tiles\n"
	stats_text += "Average Quality: " + str(int(stats["average_quality"])) + "\n"
	room_stats.text = stats_text

func update_season_panel():
	if not GameManager.instance or not GameManager.instance.season_system:
		return
	
	# 更新季节信息
	var season_info_data = GameManager.instance.season_system.get_season_info()
	var info_text = "Season: " + season_info_data["season"] + "\n"
	info_text += "Day: " + str(season_info_data["day"]) + "/" + str(season_info_data["days_per_season"]) + "\n"
	info_text += "Time: " + season_info_data["time_of_day"] + "\n"
	info_text += "Temperature: " + str(int(season_info_data["temperature"])) + "°C\n"
	info_text += "Weather: " + season_info_data["weather"] + "\n"
	season_info.text = info_text
	
	# 更新天气预报
	var forecast = GameManager.instance.season_system.get_weather_forecast()
	var forecast_text = "Weather Forecast:\n"
	for day_forecast in forecast:
		forecast_text += "Day " + str(day_forecast["day"]) + ": " + day_forecast["weather"] + "\n"
	weather_forecast.text = forecast_text

func update_story_panel():
	if not GameManager.instance or not GameManager.instance.storyteller_system:
		return
	
	# 更新活跃故事
	active_stories.clear()
	for arc in GameManager.instance.storyteller_system.active_story_arcs:
		var story_text = arc.name + " (Stage " + str(arc.current_stage + 1) + "/" + str(arc.events.size()) + ")"
		active_stories.add_item(story_text)
		active_stories.set_item_metadata(active_stories.get_item_count() - 1, arc)
	
	# 更新故事历史
	story_history.clear()
	var stats = GameManager.instance.storyteller_system.get_story_statistics()
	for event in stats["recent_events"]:
		story_history.add_item(event["title"])

# UI事件处理
func _on_colonist_mood_selected(index: int):
	selected_colonist = colonist_mood_list.get_item_metadata(index)
	if selected_colonist and GameManager.instance.mood_system:
		var mood_info = GameManager.instance.mood_system.get_mood_info(selected_colonist)
		var details_text = "Colonist: " + selected_colonist.colonist_name + "\n"
		details_text += "Current Mood: " + str(int(mood_info["current_mood"])) + "\n"
		details_text += "Base Mood: " + str(int(mood_info["base_mood"])) + "\n"
		details_text += "Mental State: " + mood_info["mental_state"] + "\n\n"
		details_text += "Mood Modifiers:\n"
		for modifier in mood_info["modifiers"]:
			details_text += "• " + modifier["name"] + ": " + str(modifier["effect"]) + "\n"
		mood_details.text = details_text

func _on_health_colonist_selected(index: int):
	selected_colonist = health_overview.get_item_metadata(index)
	if selected_colonist and GameManager.instance.medical_system:
		var health_info = GameManager.instance.medical_system.get_health_info(selected_colonist)
		var details_text = "Colonist: " + selected_colonist.colonist_name + "\n"
		details_text += "Overall Health: " + str(int(health_info["overall_health"])) + "%\n"
		details_text += "Pain Level: " + str(int(health_info["pain_level"])) + "\n"
		details_text += "Consciousness: " + str(int(health_info["consciousness"])) + "%\n\n"
		
		if health_info["injuries"].size() > 0:
			details_text += "Injuries:\n"
			for injury in health_info["injuries"]:
				details_text += "• " + injury["description"] + " (Severity: " + str(int(injury["severity"])) + ")\n"
		
		if health_info["diseases"].size() > 0:
			details_text += "\nDiseases:\n"
			for disease in health_info["diseases"]:
				details_text += "• " + disease["type"] + " (Severity: " + str(int(disease["severity"])) + ")\n"
		
		injury_details.text = details_text

func _on_combatant_selected(index: int):
	var combatant = combatant_list.get_item_metadata(index)
	if combatant and GameManager.instance.combat_system:
		var combat_info = GameManager.instance.combat_system.get_combat_info(combatant)
		print("Selected combatant: ", combat_info)

func _on_work_colonist_selected(index: int):
	selected_colonist = colonist_work_list.get_item_metadata(index)
	if selected_colonist and GameManager.instance.work_system:
		var work_info = GameManager.instance.work_system.get_colonist_work_info(selected_colonist)
		print("Selected work colonist: ", work_info)

func _on_room_selected(index: int):
	selected_room = room_list.get_item_metadata(index)
	if selected_room:
		var room_info = selected_room.get_room_info()
		var details_text = "Room Type: " + room_info["type"] + "\n"
		details_text += "Quality: " + room_info["quality"] + "\n"
		details_text += "Area: " + str(room_info["area"]) + " tiles\n"
		details_text += "Temperature: " + str(int(room_info["temperature"])) + "°C\n"
		details_text += "Cleanliness: " + str(int(room_info["cleanliness"])) + "%\n"
		details_text += "Beauty: " + str(int(room_info["beauty"])) + "\n"
		details_text += "Impressiveness: " + str(int(room_info["impressiveness"])) + "\n"
		details_text += "Mood Modifier: " + str(room_info["mood_modifier"]) + "\n"
		room_details.text = details_text

func _on_story_selected(index: int):
	var story_arc = active_stories.get_item_metadata(index)
	if story_arc:
		var details_text = "Story: " + story_arc.name + "\n"
		details_text += "Description: " + story_arc.description + "\n"
		details_text += "Current Stage: " + str(story_arc.current_stage + 1) + "/" + str(story_arc.events.size()) + "\n"
		details_text += "Active: " + str(story_arc.is_active) + "\n"
		story_details.text = details_text

func _on_force_combat_pressed():
	if GameManager.instance.colonists.size() >= 2:
		var colonist1 = GameManager.instance.colonists[0]
		var colonist2 = GameManager.instance.colonists[1]
		GameManager.instance.start_combat(colonist1, colonist2)

# 系统信号处理
func _on_mood_changed(_colonist: Colonist, _new_mood: float):
	if tab_container.current_tab == 0:
		update_mood_panel()

func _on_mental_break(colonist: Colonist, break_type: String):
	print("Mental break: ", colonist.colonist_name, " - ", break_type)

func _on_injury_sustained(_colonist: Colonist, _injury):
	if tab_container.current_tab == 1:
		update_medical_panel()

func _on_story_event(event):
	if tab_container.current_tab == 6:
		update_story_panel()
	print("Story event: ", event.title)

# 新系统的更新函数
func update_faction_panel():
	if not GameManager.instance or not GameManager.instance.faction_system:
		return

	faction_list.clear()
	for faction in GameManager.instance.faction_system.known_factions:
		var faction_info = faction.get_faction_info()
		var faction_text = faction_info["name"] + " (" + faction_info["diplomatic_status"] + ")"
		faction_list.add_item(faction_text)
		faction_list.set_item_metadata(faction_list.get_item_count() - 1, faction)

func update_vehicle_panel():
	if not GameManager.instance or not GameManager.instance.vehicle_system:
		return

	vehicle_list.clear()
	for vehicle in GameManager.instance.vehicle_system.vehicles:
		var vehicle_info = vehicle.get_vehicle_info()
		var vehicle_text = vehicle_info["name"] + " - " + vehicle_info["status"]
		vehicle_list.add_item(vehicle_text)
		vehicle_list.set_item_metadata(vehicle_list.get_item_count() - 1, vehicle)

func update_pollution_panel():
	if not GameManager.instance or not GameManager.instance.pollution_system:
		return

	var stats = GameManager.instance.pollution_system.get_environmental_statistics()
	var overview_text = "Pollution Sources: " + str(stats["total_pollution_sources"]) + "\n"
	overview_text += "Active Disasters: " + str(stats["active_disasters"]) + "\n"
	overview_text += "Ecosystem Zones: " + str(stats["ecosystem_zones"]) + "\n"
	overview_text += "Extinct Species: " + str(stats["total_extinct_species"]) + "\n"
	pollution_overview.text = overview_text

func update_religion_panel():
	if not GameManager.instance or not GameManager.instance.religion_system:
		return

	religion_list.clear()
	for religion in GameManager.instance.religion_system.religions:
		var religion_info = religion.get_religion_info()
		var religion_text = religion_info["name"] + " (" + str(religion_info["followers"]) + " followers)"
		religion_list.add_item(religion_text)
		religion_list.set_item_metadata(religion_list.get_item_count() - 1, religion)

func update_genetic_panel():
	if not GameManager.instance or not GameManager.instance.genetic_system:
		return

	genetic_profiles.clear()
	for colonist in GameManager.instance.colonists:
		if colonist in GameManager.instance.genetic_system.colonist_genetics:
			var profile = GameManager.instance.genetic_system.colonist_genetics[colonist]
			var profile_text = colonist.colonist_name + " (Genes: " + str(profile.active_genes.size()) + ", Mutations: " + str(profile.mutations.size()) + ")"
			genetic_profiles.add_item(profile_text)
			genetic_profiles.set_item_metadata(genetic_profiles.get_item_count() - 1, colonist)

func update_ai_panel():
	if not GameManager.instance or not GameManager.instance.ai_system:
		return

	ai_units_list.clear()
	for ai_unit in GameManager.instance.ai_system.ai_units:
		var ai_info = ai_unit.get_ai_info()
		var ai_text = ai_info["name"] + " - " + ai_info["status"] + " (IQ: " + str(int(ai_info["intelligence"])) + ")"
		ai_units_list.add_item(ai_text)
		ai_units_list.set_item_metadata(ai_units_list.get_item_count() - 1, ai_unit)
