extends Control
class_name DataVisualizationUI

# 数据可视化界面 - 展示游戏中的丰富数据

signal data_filter_changed(filter_type: String, value)
signal export_requested(data_type: String, format: String)

# UI组件
@onready var tab_container: TabContainer = $TabContainer
@onready var data_tree: Tree = $TabContainer/Overview/VBoxContainer/DataTree
@onready var statistics_panel: RichTextLabel = $TabContainer/Overview/VBoxContainer/StatisticsPanel
@onready var filter_container: HBoxContainer = $TabContainer/Overview/VBoxContainer/FilterContainer

# 数据展示组件
@onready var colonist_list: ItemList = $TabContainer/Colonists/VBoxContainer/ColonistList
@onready var colonist_details: RichTextLabel = $TabContainer/Colonists/VBoxContainer/ColonistDetails
@onready var skill_chart: Control = $TabContainer/Colonists/VBoxContainer/SkillChart

@onready var content_browser: ItemList = $TabContainer/Content/VBoxContainer/ContentBrowser
@onready var content_details: RichTextLabel = $TabContainer/Content/VBoxContainer/ContentDetails
@onready var content_filters: VBoxContainer = $TabContainer/Content/VBoxContainer/ContentFilters

@onready var analytics_chart: Control = $TabContainer/Analytics/VBoxContainer/AnalyticsChart
@onready var trend_display: RichTextLabel = $TabContainer/Analytics/VBoxContainer/TrendDisplay

# 数据管理
var content_manager: ContentManager
var current_filter: Dictionary = {}
var selected_data_type: String = ""
var chart_data: Dictionary = {}

func _ready():
	setup_ui()
	connect_signals()
	if GameManager.instance and GameManager.instance.has_method("get_content_manager"):
		content_manager = GameManager.instance.get_content_manager()
	refresh_all_data()

func setup_ui():
	# 设置标签页（本地化）
	tab_container.set_tab_title(0, LocalizationManager.tr_ui("overview"))
	tab_container.set_tab_title(1, LocalizationManager.tr_ui("colonists"))
	tab_container.set_tab_title(2, LocalizationManager.tr_ui("content"))
	tab_container.set_tab_title(3, LocalizationManager.tr_ui("analytics"))

	# 设置过滤器
	setup_filters()

	# 设置数据树
	setup_data_tree()

	# 添加到本地化组
	add_to_group("localizable_ui")

func connect_signals():
	tab_container.tab_changed.connect(_on_tab_changed)
	colonist_list.item_selected.connect(_on_colonist_selected)
	content_browser.item_selected.connect(_on_content_selected)
	
	if content_manager:
		content_manager.content_generated.connect(_on_content_generated)
		content_manager.content_updated.connect(_on_content_updated)

func setup_filters():
	# 清除现有过滤器
	for child in filter_container.get_children():
		child.queue_free()

	# 创建过滤器控件（本地化）
	var type_filter = OptionButton.new()
	type_filter.name = "TypeFilter"
	type_filter.add_item(LocalizationManager.tr_ui("all_types"))

	var content_types = ["colonists", "animals", "plants", "weapons", "armor", "buildings"]
	for content_type in content_types:
		var localized_name = LocalizationManager.tr_ui(content_type)
		type_filter.add_item(localized_name)

	type_filter.item_selected.connect(_on_type_filter_changed)
	filter_container.add_child(type_filter)

	# 搜索框（本地化）
	var search_box = LineEdit.new()
	search_box.name = "SearchBox"
	search_box.placeholder_text = LocalizationManager.tr_ui("search_placeholder")
	search_box.text_changed.connect(_on_search_changed)
	filter_container.add_child(search_box)

func setup_data_tree():
	data_tree.set_column_titles_visible(true)
	data_tree.set_columns(3)
	data_tree.set_column_title(0, LocalizationManager.tr_ui("category"))
	data_tree.set_column_title(1, LocalizationManager.tr_ui("count"))
	data_tree.set_column_title(2, LocalizationManager.tr_ui("status"))

func refresh_all_data():
	refresh_overview()
	refresh_colonist_data()
	refresh_content_data()
	refresh_analytics()

func refresh_overview():
	# 清空数据树
	data_tree.clear()
	var root = data_tree.create_item()
	root.set_text(0, "Game Data")
	
	if not content_manager:
		return
	
	var stats = content_manager.get_content_statistics()
	
	# 添加内容类型统计
	var content_node = data_tree.create_item(root)
	content_node.set_text(0, "Content Types")
	content_node.set_text(1, str(stats["content_types"].size()))
	content_node.set_text(2, "Active")
	
	for content_type in stats["content_types"]:
		var type_node = data_tree.create_item(content_node)
		type_node.set_text(0, content_type.capitalize())
		type_node.set_text(1, str(stats["content_types"][content_type]))
		type_node.set_text(2, "Loaded")
	
	# 添加系统统计
	if GameManager.instance:
		add_system_stats_to_tree(root)
	
	# 更新统计面板
	update_statistics_panel(stats)

func add_system_stats_to_tree(root: TreeItem):
	var systems_node = data_tree.create_item(root)
	systems_node.set_text(0, "Game Systems")
	systems_node.set_text(2, "Running")
	
	var system_stats = [
		{"name": "Colonists", "count": GameManager.instance.colonists.size()},
		{"name": "Resources", "count": GameManager.instance.resources.size()},
		{"name": "Buildings", "count": GameManager.instance.buildings.size()},
		{"name": "Animals", "count": GameManager.instance.animals.size()}
	]
	
	var total_count = 0
	for stat in system_stats:
		var stat_node = data_tree.create_item(systems_node)
		stat_node.set_text(0, stat["name"])
		stat_node.set_text(1, str(stat["count"]))
		stat_node.set_text(2, "Active")
		total_count += stat["count"]
	
	systems_node.set_text(1, str(total_count))

func update_statistics_panel(stats: Dictionary):
	var text = "[b]Game Statistics[/b]\n\n"
	text += "Total Items: " + str(stats["total_items"]) + "\n"
	text += "Content Types: " + str(stats["content_types"].size()) + "\n\n"
	
	text += "[b]Content Distribution:[/b]\n"
	for content_type in stats["content_types"]:
		var count = stats["content_types"][content_type]
		var percentage = (float(count) / stats["total_items"] * 100) if stats["total_items"] > 0 else 0
		text += "• " + content_type.capitalize() + ": " + str(count) + " (" + "%.1f" % percentage + "%)\n"
	
	text += "\n[b]Cache Usage:[/b]\n"
	for cache_type in stats.get("cache_usage", {}):
		text += "• " + cache_type.capitalize() + ": " + str(stats["cache_usage"][cache_type]) + " cached\n"
	
	statistics_panel.text = text

func refresh_colonist_data():
	colonist_list.clear()
	
	if not GameManager.instance:
		return
	
	for colonist in GameManager.instance.colonists:
		var item_text = colonist.colonist_name + " (Age: " + str(colonist.age) + ")"
		colonist_list.add_item(item_text)
		colonist_list.set_item_metadata(colonist_list.get_item_count() - 1, colonist)

func refresh_content_data():
	content_browser.clear()
	
	if not content_manager:
		return
	
	var content_type = get_selected_content_type()
	var content = content_manager.get_content(content_type)
	
	for item in content:
		var item_text = item.get("name", "Unknown") + " (" + item.get("type", "Unknown") + ")"
		content_browser.add_item(item_text)
		content_browser.set_item_metadata(content_browser.get_item_count() - 1, item)

func refresh_analytics():
	if not content_manager:
		return
	
	var analytics_text = "[b]Content Analytics[/b]\n\n"
	
	# 分析每种内容类型
	var content_types = ["colonists", "animals", "plants", "weapons", "armor"]
	for content_type in content_types:
		var analysis = content_manager.analyze_content_distribution(content_type)
		analytics_text += "[b]" + content_type.capitalize() + ":[/b]\n"
		analytics_text += "Total: " + str(analysis["total_count"]) + "\n"
		
		if analysis["type_distribution"].size() > 0:
			analytics_text += "Types: "
			for type_name in analysis["type_distribution"]:
				analytics_text += type_name + "(" + str(analysis["type_distribution"][type_name]) + ") "
			analytics_text += "\n"
		
		analytics_text += "\n"
	
	trend_display.text = analytics_text

func get_selected_content_type() -> String:
	var type_filter = filter_container.get_node("TypeFilter") as OptionButton
	if type_filter and type_filter.selected > 0:
		var content_types = ["colonists", "animals", "plants", "weapons", "armor", "buildings"]
		return content_types[type_filter.selected - 1]
	return "colonists"

# 信号处理
func _on_tab_changed(tab: int):
	match tab:
		0: refresh_overview()
		1: refresh_colonist_data()
		2: refresh_content_data()
		3: refresh_analytics()

func _on_colonist_selected(index: int):
	var colonist = colonist_list.get_item_metadata(index)
	if colonist:
		display_colonist_details(colonist)

func _on_content_selected(index: int):
	var item = content_browser.get_item_metadata(index)
	if item:
		display_content_details(item)

func _on_type_filter_changed(index: int):
	refresh_content_data()
	data_filter_changed.emit("type", index)

func _on_search_changed(text: String):
	filter_content_by_search(text)

func _on_content_generated(_content_type: String, _items: Array):
	if tab_container.current_tab == 2:  # Content tab
		refresh_content_data()
	refresh_overview()

func _on_content_updated(_content_type: String, _item_id: String):
	refresh_content_data()

# 详细信息显示
func display_colonist_details(colonist):
	var details = "[b]" + colonist.colonist_name + "[/b]\n\n"
	details += LocalizationManager.tr_ui("age") + ": " + str(colonist.age) + "\n"
	details += LocalizationManager.tr_ui("health") + ": " + "%.1f" % colonist.current_health + "/" + "%.1f" % colonist.max_health + "\n"
	details += LocalizationManager.tr_ui("mood") + ": " + "%.1f" % colonist.current_mood + "\n\n"

	details += "[b]" + LocalizationManager.tr_ui("skills") + ":[/b]\n"
	for skill_name in colonist.skills:
		var skill_level = colonist.skills[skill_name]
		var localized_skill = LocalizationManager.tr_data("skill", skill_name)
		details += "• " + localized_skill + ": " + str(skill_level) + "\n"

	if colonist.traits.size() > 0:
		details += "\n[b]" + LocalizationManager.tr_ui("traits") + ":[/b]\n"
		for _trait in colonist.traits:
			var localized_trait = LocalizationManager.tr_data("trait", _trait)
			details += "• " + localized_trait + "\n"

	colonist_details.text = details

func display_content_details(item: Dictionary):
	var details = "[b]" + item.get("name", "Unknown") + "[/b]\n\n"
	details += "Type: " + item.get("type", "Unknown") + "\n"
	details += "ID: " + item.get("id", "Unknown") + "\n\n"
	
	# 显示主要属性
	var important_fields = ["health", "damage", "market_value", "work_to_make", "description"]
	for field in important_fields:
		if field in item:
			var value = item[field]
			details += field.replace("_", " ").capitalize() + ": " + str(value) + "\n"
	
	# 显示材料需求
	if "materials" in item:
		details += "\n[b]Materials:[/b]\n"
		for material_name in item["materials"]:
			details += "• " + material_name.capitalize() + ": " + str(item["materials"][material_name]) + "\n"
	
	# 显示效果
	if "effects" in item:
		details += "\n[b]Effects:[/b]\n"
		for effect in item["effects"]:
			details += "• " + effect.replace("_", " ").capitalize() + ": " + str(item["effects"][effect]) + "\n"
	
	content_details.text = details

func filter_content_by_search(search_text: String):
	if not content_manager or search_text.is_empty():
		refresh_content_data()
		return
	
	content_browser.clear()
	var content_type = get_selected_content_type()
	var results = content_manager.search_content(content_type, search_text)
	
	for item in results:
		var item_text = item.get("name", "Unknown") + " (" + item.get("type", "Unknown") + ")"
		content_browser.add_item(item_text)
		content_browser.set_item_metadata(content_browser.get_item_count() - 1, item)

# 数据导出功能
func export_data(data_type: String, format: String = "json"):
	if not content_manager:
		return
	
	var timestamp = Time.get_datetime_string_from_system().replace(":", "-").replace(" ", "_")
	var filename = "game_data_" + data_type + "_" + timestamp + "." + format
	var file_path = "user://exports/" + filename
	
	# 确保导出目录存在
	if not DirAccess.dir_exists_absolute("user://exports/"):
		DirAccess.open("user://").make_dir("exports")
	
	var success = false
	match format:
		"json":
			success = content_manager.export_content(data_type, file_path)
		"csv":
			success = export_to_csv(data_type, file_path)
	
	if success:
		print("Data exported to: ", file_path)
		export_requested.emit(data_type, format)
	else:
		print("Failed to export data")

func export_to_csv(data_type: String, file_path: String) -> bool:
	var content = content_manager.get_content(data_type)
	if content.is_empty():
		return false
	
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	if not file:
		return false
	
	# 写入CSV头部
	var headers = []
	if content.size() > 0:
		headers = content[0].keys()
		file.store_line(",".join(headers))
	
	# 写入数据行
	for item in content:
		var row = []
		for header in headers:
			var value = str(item.get(header, ""))
			# 转义CSV中的逗号和引号
			if "," in value or "\"" in value:
				value = "\"" + value.replace("\"", "\"\"") + "\""
			row.append(value)
		file.store_line(",".join(row))
	
	file.close()
	return true

# 数据生成控制
func generate_sample_data():
	if content_manager:
		content_manager.generate_starter_content()
		refresh_all_data()

func clear_all_data():
	if content_manager:
		for content_type in ["colonists", "animals", "plants", "weapons", "armor", "buildings"]:
			content_manager.dynamic_content[content_type] = []
		refresh_all_data()

# 本地化更新
func update_localization():
	setup_ui()
	refresh_all_data()

# 实时数据更新
func _process(_delta: float):
	# 每秒更新一次统计信息
	if fmod(Time.get_time_dict_from_system()["second"], 5) == 0:
		if tab_container.current_tab == 0:  # Overview tab
			refresh_overview()
