[folding]

node_unfolds=[NodePath("."), PackedStringArray("Transform"), NodePath("UI/Background"), PackedStringArray("Layout", "Mouse"), NodePath("UI/TopPanel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer/FoodLabel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer/WoodLabel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer/StoneLabel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer/SteelLabel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/ResourceContainer/MedicineLabel"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer/PauseButton"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer/Speed1Button"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer/Speed2Button"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer/Speed3Button"), PackedStringArray("Layout"), NodePath("UI/TopPanel/TimeContainer/TimeLabel"), PackedStringArray("Layout"), NodePath("UI/BottomPanel"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer/BuildButton"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer/ResearchButton"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer/TradeButton"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer/SystemsButton"), PackedStringArray("Layout"), NodePath("UI/BottomPanel/ButtonContainer/LanguageButton"), PackedStringArray("Layout"), NodePath("UI/InfoPanel"), PackedStringArray("Layout"), NodePath("UI/InfoPanel/InfoContainer"), PackedStringArray("Layout"), NodePath("UI/InfoPanel/InfoContainer/InfoTitle"), PackedStringArray("Layout"), NodePath("UI/InfoPanel/InfoContainer/HSeparator"), PackedStringArray("Layout"), NodePath("UI/InfoPanel/InfoContainer/InfoText"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
