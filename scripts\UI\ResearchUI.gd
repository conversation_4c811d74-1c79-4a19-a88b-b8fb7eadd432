extends Control
class_name ResearchUI

# 研究界面 - 显示科技树和研究进度

signal research_selected(tech_id: String)

@onready var research_tree: Tree = $VBoxContainer/ResearchTree
@onready var research_info: RichTextLabel = $VBoxContainer/ResearchInfo
@onready var current_research_label: Label = $VBoxContainer/CurrentResearch
@onready var research_progress: ProgressBar = $VBoxContainer/ProgressBar

var research_system: ResearchSystem
var selected_tech_id: String = ""

func _ready():
	setup_ui()
	if GameManager.instance and GameManager.instance.research_system:
		research_system = GameManager.instance.research_system
		research_system.research_started.connect(_on_research_started)
		research_system.research_completed.connect(_on_research_completed)
		populate_research_tree()

func setup_ui():
	# 设置研究树
	research_tree.set_columns(3)
	research_tree.set_column_titles_visible(true)
	research_tree.set_column_title(0, "Technology")
	research_tree.set_column_title(1, "Cost")
	research_tree.set_column_title(2, "Status")
	
	research_tree.item_selected.connect(_on_tree_item_selected)
	research_tree.item_activated.connect(_on_tree_item_activated)

func populate_research_tree():
	if not research_system:
		return
	
	research_tree.clear()
	var root = research_tree.create_item()
	root.set_text(0, "Research Tree")
	
	# 按类别组织研究
	var categories = {}
	var all_research = research_system.get_all_research_info()
	
	for tech_id in all_research:
		var tech_info = all_research[tech_id]
		var category = tech_info["category"]
		
		if category not in categories:
			categories[category] = research_tree.create_item(root)
			categories[category].set_text(0, category)
			categories[category].set_selectable(0, false)
		
		var tech_item = research_tree.create_item(categories[category])
		tech_item.set_text(0, tech_info["name"])
		tech_item.set_text(1, str(tech_info["cost"]))
		
		# 设置状态
		if tech_info["completed"]:
			tech_item.set_text(2, "Completed")
			tech_item.set_custom_color(0, Color.GREEN)
		elif tech_info["can_research"]:
			tech_item.set_text(2, "Available")
			tech_item.set_custom_color(0, Color.WHITE)
		else:
			tech_item.set_text(2, "Locked")
			tech_item.set_custom_color(0, Color.GRAY)
		
		# 存储技术ID
		tech_item.set_metadata(0, tech_id)
		
		# 显示进度
		if tech_info["progress"] > 0:
			var progress_text = str(tech_info["progress"]) + "/" + str(tech_info["cost"])
			tech_item.set_text(2, progress_text)

func _on_tree_item_selected():
	var selected = research_tree.get_selected()
	if selected and selected.has_meta("0"):
		selected_tech_id = selected.get_metadata(0)
		update_research_info()

func _on_tree_item_activated():
	if selected_tech_id != "" and research_system:
		if research_system.start_research(selected_tech_id):
			research_selected.emit(selected_tech_id)
			update_current_research_display()

func update_research_info():
	if selected_tech_id == "" or not research_system:
		research_info.text = "Select a technology to view details"
		return
	
	var tech_info = research_system.get_research_info(selected_tech_id)
	if tech_info.is_empty():
		return
	
	var info_text = "[b]" + tech_info["name"] + "[/b]\n\n"
	info_text += tech_info["description"] + "\n\n"
	info_text += "[b]Research Cost:[/b] " + str(tech_info["cost"]) + "\n"
	info_text += "[b]Progress:[/b] " + str(tech_info["progress"]) + "/" + str(tech_info["cost"]) + "\n\n"
	
	if tech_info["prerequisites"].size() > 0:
		info_text += "[b]Prerequisites:[/b]\n"
		for prereq in tech_info["prerequisites"]:
			var prereq_info = research_system.get_research_info(prereq)
			if not prereq_info.is_empty():
				info_text += "• " + prereq_info["name"] + "\n"
		info_text += "\n"
	
	if tech_info["unlocks"].size() > 0:
		info_text += "[b]Unlocks:[/b]\n"
		for unlock in tech_info["unlocks"]:
			info_text += "• " + unlock.replace("_", " ").capitalize() + "\n"
	
	research_info.text = info_text

func update_current_research_display():
	if not research_system:
		return
	
	if research_system.current_research != "":
		var tech_info = research_system.get_research_info(research_system.current_research)
		if not tech_info.is_empty():
			current_research_label.text = "Researching: " + tech_info["name"]
			research_progress.max_value = tech_info["cost"]
			research_progress.value = tech_info["progress"]
			research_progress.visible = true
		else:
			current_research_label.text = "No active research"
			research_progress.visible = false
	else:
		current_research_label.text = "No active research"
		research_progress.visible = false

func _on_research_started(tech_id: String):
	update_current_research_display()
	populate_research_tree()  # 刷新树状图

func _on_research_completed(tech_id: String):
	update_current_research_display()
	populate_research_tree()  # 刷新树状图
	
	# 显示完成通知
	var tech_info = research_system.get_research_info(tech_id)
	if not tech_info.is_empty():
		show_research_completed_notification(tech_info["name"])

func show_research_completed_notification(tech_name: String):
	# 创建通知弹窗
	var notification = AcceptDialog.new()
	notification.dialog_text = "Research Completed: " + tech_name
	notification.title = "Research Complete"
	add_child(notification)
	notification.popup_centered()
	
	# 自动关闭
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.timeout.connect(func(): notification.queue_free())
	timer.autostart = true
	notification.add_child(timer)

func refresh_ui():
	if research_system:
		populate_research_tree()
		update_current_research_display()
		if selected_tech_id != "":
			update_research_info()

func _process(_delta):
	# 定期更新进度条
	if research_system and research_system.current_research != "":
		var tech_info = research_system.get_research_info(research_system.current_research)
		if not tech_info.is_empty():
			research_progress.value = tech_info["progress"]
